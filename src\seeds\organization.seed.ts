import mongoose from "mongoose";
import { connectToDatabase } from "../config/mongodb";
import { Organization } from "../models/organization.model";

await connectToDatabase();

async function seedOrganizations() {
	const organizations = [
		{
			name: "PlacedHQ",
			address: "Incubex 21, Masai School, HSR, Bangalore",
			domain: "placedhq.ai",
		},
	];

	const createdOrgs = await Organization.insertMany(organizations);

	console.log("Seeded organizations:", createdOrgs);

	await mongoose.disconnect();
}

seedOrganizations().catch((err) => {
	console.error(err);
	process.exit(1);
});
