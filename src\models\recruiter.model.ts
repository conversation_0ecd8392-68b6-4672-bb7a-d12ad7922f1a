import mongoose, {
	type Document,
	type Model,
	Schema,
	type Types,
} from "mongoose";

export interface IRecruiter {
	_id: Types.ObjectId;
	name: string;
	email: string;
	organization: Types.ObjectId;
	password: string;
	createdAt: Date;
	updatedAt: Date;
}

type RecruiterDocument = IRecruiter &
	Document & {
		comparePassword(password: string): Promise<boolean>;
		generateToken(): string;
	};

interface RecruiterModel extends Model<RecruiterDocument> {
	findByToken(token: string): Promise<RecruiterDocument | null>;
}

const RecruiterSchema: Schema = new Schema<RecruiterDocument>(
	{
		name: { type: String, required: true },
		email: { type: String, required: true, unique: true },
		organization: {
			type: Schema.Types.ObjectId,
			ref: "Organization",
			required: true,
		},
		password: { type: String, required: true },
	},
	{ timestamps: true },
);

RecruiterSchema.methods.comparePassword = async function (
	password: string,
): Promise<boolean> {
	return password === this.password;
};

RecruiterSchema.methods.generateToken = function (): string {
	// This is a placeholder implementation. Replace with actual token generation logic.
	// For example, you might use JWT and the recruiter's _id.
	return `token-for-${this._id}`;
};

RecruiterSchema.statics.findByToken = async function (
	token: string,
): Promise<RecruiterDocument | null> {
	// This is a placeholder implementation. Replace with the actual logic to extract _id or data from token.
	// For example, if your token is of the format 'token-for-<id>', extract <id>.
	const matches = token.match(/^token-for-(.+)$/);
	if (!matches) return null;
	const recruiterId = matches[1];
	return this.findById(recruiterId);
};

export const Recruiter = mongoose.model<RecruiterDocument, RecruiterModel>(
	"recruiter",
	RecruiterSchema,
);
