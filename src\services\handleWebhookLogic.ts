import { AgentTask, TaskStatus } from "../models/agentTask.model";
import Candidate, { StageStatus } from "../models/candidate.model";
import { triggerNextStage } from "../services/workflow.orchestrator";

export const webhookLogic = async (
	candidateId: string,
	stageName: string,
	outcome: string,
	resultPayload: unknown,
) => {
	// 1. Find and update the specific AgentTask
	const agentTask = await AgentTask.findOneAndUpdate(
		{
			candidateId,
			stage: stageName,
			status: TaskStatus.IN_PROGRESS, // Find the task that was started but not finished
		},
		{
			status: TaskStatus.COMPLETED,
			outcome: outcome,
			result: resultPayload,
			finishedAt: new Date(),
		},
		{ sort: { createdAt: -1 } }, // Get the most recent one
	);

	if (!agentTask) {
		throw new Error(
			`Webhook Error: Could not find an active AgentTask for candidate ${candidateId} at stage ${stageName}`,
		);
	}

	// 2. Update the candidate's master status
	const newCandidateStatus =
		outcome === "fail" || outcome === "bad"
			? StageStatus.COMPLETED_FAIL
			: StageStatus.COMPLETED_SUCCESS;
	await Candidate.findByIdAndUpdate(candidateId, {
		status: newCandidateStatus,
	});

	// 3. Trigger the next stage in the workflow
	await triggerNextStage(candidateId, outcome);
};
