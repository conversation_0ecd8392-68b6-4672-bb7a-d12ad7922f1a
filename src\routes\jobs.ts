import Elysia from "elysia";
import Candidate from "../models/candidate.model";
import Job from "../models/job.model";

export const jobsApp = new Elysia({ prefix: "/jobs" })
	.get("/:jobId", async ({ params }) => {
		const { jobId } = params;
		const job = await Job.findById(jobId);
		if (!job) return { error: "Job not found" };
		return job;
	})
	.get("/:jobId/candidates", async () => {
		const candidates = await Candidate.find({});
		return candidates;
	});
