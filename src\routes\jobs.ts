import Elysia from "elysia";
import Candidate from "../models/candidate.model";
import Job from "../models/job.model";
import { ensureAuth } from "../services/auth";

export const jobsApp = new Elysia({ prefix: "/jobs" })
	.use(ensureAuth)
	.get("/:jobId", async ({ params, user }) => {
		const { jobId } = params;
		const job = await Job.findOne({
			_id: jobId,
			organization: user.organization,
		});
		if (!job) return { error: "Job not found" };
		return job;
	})
	.get("/:jobId/candidates", async () => {
		const candidates = await Candidate.find({});
		return candidates;
	});
