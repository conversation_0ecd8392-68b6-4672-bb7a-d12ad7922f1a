import type { Job } from "bullmq";
import { AgentTask, TaskStatus } from "../models/agentTask.model"; // <-- Import AgentTask
import Candidate, { StageStatus } from "../models/candidate.model";
import { JobConfigModel } from "../models/jobConfig.model";
import { dispatchAgentTask } from "../services/agent.service";

export const processStageJob = async (job: Job) => {
	const { candidateId, jobId, currentStage } = job.data;

	console.log(
		`Processing job for candidate ${candidateId} at stage '${currentStage}'`,
	);

	// Find the config to get the agentId
	const jobConfig = await JobConfigModel.findOne({ jobId }).lean();
	const stageConfig = jobConfig?.stageConfig.find(
		(sc) => sc.stage === currentStage,
	);
	if (!stageConfig) {
		throw new Error(`Configuration for stage '${currentStage}' not found.`);
	}

	// CREATE THE AGENT TASK HERE
	const agentTask = await AgentTask.create({
		jobId,
		candidateId,
		agentId: stageConfig.action.agentId,
		stage: currentStage,
		status: TaskStatus.IN_PROGRESS,
		attempts: (job.attemptsMade || 0) + 1,
		queueName: job.queueName,
		startedAt: new Date(),
	});

	try {
		await Candidate.findByIdAndUpdate(candidateId, {
			status: StageStatus.IN_PROGRESS,
		});

		await dispatchAgentTask(
			stageConfig.action.agentId,
			stageConfig.action.params,
			stageConfig.action.outputs || [],
			jobId,
			candidateId,
		);

		await Candidate.findByIdAndUpdate(candidateId, {
			status: StageStatus.AWAITING_RESULT,
		});
		console.log(
			`Task for candidate ${candidateId} initiated. Awaiting webhook.`,
		);
	} catch (error) {
		const errorMessage =
			error instanceof Error ? error.message : "An unknown error occurred.";
		console.error(
			`Error processing job for candidate ${candidateId}:`,
			errorMessage,
		);

		// UPDATE THE AGENT TASK ON FAILURE
		agentTask.status = TaskStatus.FAILED;
		agentTask.error = errorMessage;
		agentTask.finishedAt = new Date();
		await agentTask.save();

		await Candidate.findByIdAndUpdate(candidateId, {
			status: StageStatus.ERROR,
		});
		throw error;
	}
};
