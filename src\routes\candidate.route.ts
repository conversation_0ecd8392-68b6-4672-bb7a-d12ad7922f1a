import Elysia, { t } from "elysia";
import Candidate, { type ICandidate } from "../models/candidate.model";
import { ensureAuth } from "../services/auth";
import csv from "csv-parser";
import { Readable } from "stream";
import mongoose from "mongoose";

// Helper function to validate and transform CSV row data
function validateAndTransformCsvRow(data: any, rowIndex: number): { candidate: Partial<ICandidate>, errors: string[] } {
    const errors: string[] = [];
    const candidate: Partial<ICandidate> = {};

    // Required fields validation
    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
        errors.push(`Row ${rowIndex}: Name is required`);
    } else {
        candidate.name = data.name.trim();
    }

    if (!data.email || typeof data.email !== 'string') {
        errors.push(`Row ${rowIndex}: Email is required`);
    } else {
        const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
        if (!emailRegex.test(data.email)) {
            errors.push(`Row ${rowIndex}: Invalid email format`);
        } else {
            candidate.email = data.email.toLowerCase().trim();
        }
    }

    if (!data.resumeLink || typeof data.resumeLink !== 'string' || data.resumeLink.trim().length === 0) {
        errors.push(`Row ${rowIndex}: Resume link is required`);
    } else {
        candidate.resumeLink = data.resumeLink.trim();
    }

    if (!data.jobId) {
        errors.push(`Row ${rowIndex}: Job ID is required`);
    } else {
        const jobIdStr = data.jobId.toString().trim();
        if (!mongoose.Types.ObjectId.isValid(jobIdStr)) {
            errors.push(`Row ${rowIndex}: Invalid Job ID format (received: "${jobIdStr}")`);
        } else {
            candidate.jobId = mongoose.Types.ObjectId.createFromHexString(jobIdStr);
        }
    }

    if (!data.source || typeof data.source !== 'string' || data.source.trim().length === 0) {
        errors.push(`Row ${rowIndex}: Source is required`);
    } else {
        candidate.source = data.source.trim();
    }

    // Optional fields
    if (data.phone && typeof data.phone === 'string' && data.phone.trim().length > 0) {
        const phoneRegex = /^[+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(data.phone.trim())) {
            errors.push(`Row ${rowIndex}: Invalid phone number format`);
        } else {
            candidate.phone = data.phone.trim();
        }
    }

    if (data.stage && typeof data.stage === 'string' && data.stage.trim().length > 0) {
        candidate.stage = data.stage.trim();
    } else {
        candidate.stage = "registered"; // Default value
    }

    if (data.expectedSalary && typeof data.expectedSalary === 'string' && data.expectedSalary.trim().length > 0) {
        const salary = parseFloat(data.expectedSalary);
        if (isNaN(salary) || salary < 0) {
            errors.push(`Row ${rowIndex}: Invalid expected salary`);
        } else {
            candidate.expectedSalary = salary;
        }
    }

    // Contact info validation
    const contactInfo: any = {};

    // Use email as primary contact email if contactInfo.email is not provided
    if (data['contactInfo.email'] && typeof data['contactInfo.email'] === 'string') {
        const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
        if (!emailRegex.test(data['contactInfo.email'])) {
            errors.push(`Row ${rowIndex}: Invalid contact email format`);
        } else {
            contactInfo.email = data['contactInfo.email'].toLowerCase().trim();
        }
    } else if (candidate.email) {
        contactInfo.email = candidate.email;
    }

    if (data['contactInfo.phone'] && typeof data['contactInfo.phone'] === 'string') {
        contactInfo.phone = data['contactInfo.phone'].trim();
    }

    if (data['contactInfo.linkedin'] && typeof data['contactInfo.linkedin'] === 'string') {
        contactInfo.linkedin = data['contactInfo.linkedin'].trim();
    }

    if (data['contactInfo.github'] && typeof data['contactInfo.github'] === 'string') {
        contactInfo.github = data['contactInfo.github'].trim();
    }

    if (data['contactInfo.address'] && typeof data['contactInfo.address'] === 'string') {
        contactInfo.address = data['contactInfo.address'].trim();
    }

    candidate.contactInfo = contactInfo;

    return { candidate, errors };
}

export const candidateRoute = new Elysia({ prefix: "/candidate" })
    .post("/", async ({ body }: { body: ICandidate }) => {
        try {
            const newCandidate = await Candidate.create(body);
            return newCandidate;
        } catch (err: any) {
            return { error: "Error creating candidate", details: err?.message || String(err) };
        }
    })
    .get("/", async () => {
        try {
            const candidates = await Candidate.find({});
            return candidates;
        } catch (err: any) {
            return { error: "Error fetching candidates", details: err?.message || String(err) };
        }
    })
    .get("/:id", async ({ params: { id } }) => {
        try {
            const candidate = await Candidate.findById(id);
            if (!candidate) {
                return { error: "Candidate not found" };
            }
            return candidate;
        } catch (err: any) {
            return { error: "Error fetching candidate", details: err?.message || String(err) };
        }
    })
    .put("/:id", async ({ params: { id }, body }: { params: { id: string }, body: Partial<ICandidate> }) => {
        try {
            const updatedCandidate = await Candidate.findByIdAndUpdate(id, body, { new: true, runValidators: true });
            if (!updatedCandidate) {
                return { error: "Candidate not found or update failed" };
            }
            return updatedCandidate;
        } catch (err: any) {
            return { error: "Error updating candidate", details: err?.message || String(err) };
        }
    })
    .delete("/:id", async ({ params: { id } }) => {
        try {
            const deletedCandidate = await Candidate.findByIdAndDelete(id);
            if (!deletedCandidate) {
                return { error: "Candidate not found or already deleted" };
            }
            return { deletedCandidate };
        } catch (err: any) {
            return { error: "Error deleting candidate", details: err?.message || String(err) };
        }
    })
    .get("/job/:jobId", async ({ params: { jobId } }) => {
        try {
            const candidates = await Candidate.find({ jobId });
            return candidates;
        } catch (err: any) {
            return { error: "Error fetching candidates for the job", details: err?.message || String(err) };
        }
    })
    .put("/:id/stage", async ({ params: { id }, body }: { params: { id: string }, body: { stage: string } }) => {
        try {
            const updatedCandidate = await Candidate.findByIdAndUpdate(id, { stage: body.stage }, { new: true, runValidators: true });
            if (!updatedCandidate) {
                return { error: "Candidate not found or stage update failed" };
            }
            return updatedCandidate;
        } catch (err: any) {
            return { error: "Error updating candidate stage", details: err?.message || String(err) };
        }
    })
    .get("/search", async ({ query }: { query: { name?: string; email?: string } }) => {
        const searchCriteria: Record<string, any> = {};
        if (query.name) searchCriteria.name = { $regex: query.name, $options: "i" };
        if (query.email) searchCriteria.email = { $regex: query.email, $options: "i" };
        try {
            const candidates = await Candidate.find(searchCriteria);
            return candidates;
        } catch (err: any) {
            return { error: "Error searching candidates", details: err?.message || String(err) };
        }
    })
    .get("/filter", async ({ query }: { query: { stage?: string; status?: string; jobId?: string } }) => {
        const filterCriteria: Record<string, any> = {};
        if (query.stage) filterCriteria.stage = query.stage;
        if (query.status) filterCriteria.status = query.status;
        if (query.jobId) filterCriteria.jobId = query.jobId;
        try {
            const candidates = await Candidate.find(filterCriteria);
            return candidates;
        } catch (err: any) {
            return { error: "Error filtering candidates", details: err?.message || String(err) };
        }
    })
    .get("/available-jobs", async () => {
        try {
            // Import Job model
            const Job = (await import("../models/job.model")).default;
            const jobs = await Job.find({}).select('_id title department status');
            return {
                message: "Available jobs for CSV upload",
                jobs: jobs.map(job => ({
                    id: job._id.toString(),
                    title: job.title,
                    department: job.department,
                    status: job.status
                }))
            };
        } catch (err: any) {
            return { error: "Error fetching available jobs", details: err?.message || String(err) };
        }
    })
    // CSV Management Endpoints
    .use(ensureAuth)
    .get("/csv-template", async ({ set }) => {
        try {
            // Define CSV headers based on Candidate model required and optional fields
            const csvHeaders = [
                "name",
                "email",
                "phone",
                "resumeLink",
                "jobId",
                "stage",
                "source",
                "expectedSalary",
                "contactInfo.email",
                "contactInfo.phone",
                "contactInfo.linkedin",
                "contactInfo.github",
                "contactInfo.address"
            ];

            // Create CSV content with headers and example row
            const csvContent = [
                csvHeaders.join(","),
                // Example row with sample data
                [
                    "John Doe",
                    "<EMAIL>",
                    "+1234567890",
                    "https://example.com/resume.pdf",
                    "64f8b2c3e4b0a1b2c3d4e5fa", // Example ObjectId
                    "registered",
                    "job_board",
                    "75000",
                    "<EMAIL>",
                    "+1234567890",
                    "https://linkedin.com/in/johndoe",
                    "https://github.com/johndoe",
                    "123 Main St, City, State 12345"
                ].join(",")
            ].join("\n");

            // Set response headers for file download
            set.headers["Content-Type"] = "text/csv";
            set.headers["Content-Disposition"] = "attachment; filename=candidate_template.csv";

            return csvContent;
        } catch (err: any) {
            return { error: "Error generating CSV template", details: err?.message || String(err) };
        }
    })
    .post("/upload-csv", async ({ body, user }) => {
        try {
            const { csvData } = body;

            // Handle both string CSV and array of objects

            if (typeof csvData === 'string') {
                // Original CSV string format
                const results: any[] = [];
                const errors: string[] = [];
                let rowIndex = 0;

                return new Promise((resolve) => {
                    const stream = Readable.from([csvData]);

                    stream
                        .pipe(csv())
                        .on('data', (data) => {
                            rowIndex++;
                            try {
                                // Validate and transform the row data
                                const candidateData = validateAndTransformCsvRow(data, rowIndex);
                                if (candidateData.errors.length > 0) {
                                    errors.push(...candidateData.errors);
                                } else {
                                    // Add user context for multi-tenant isolation
                                    candidateData.candidate.createdBy = user.id.toString();
                                    results.push(candidateData.candidate);
                                }
                            } catch (err: any) {
                                errors.push(`Row ${rowIndex}: ${err.message}`);
                            }
                        })
                        .on('end', async () => {
                            try {
                                if (errors.length > 0) {
                                    resolve({
                                        error: "CSV validation failed",
                                        details: errors,
                                        validRows: results.length,
                                        totalErrors: errors.length
                                    });
                                    return;
                                }

                                if (results.length === 0) {
                                    resolve({ error: "No valid candidates found in CSV" });
                                    return;
                                }

                                // Batch insert candidates
                                const insertedCandidates = await Candidate.insertMany(results, {
                                    ordered: false // Continue inserting even if some fail
                                });

                                resolve({
                                    message: "CSV upload successful",
                                    inserted: insertedCandidates.length,
                                    candidates: insertedCandidates
                                });
                            } catch (insertErr: any) {
                                resolve({
                                    error: "Error inserting candidates",
                                    details: insertErr?.message || String(insertErr),
                                    validatedRows: results.length
                                });
                            }
                        })
                        .on('error', (err) => {
                            resolve({ error: "Error parsing CSV", details: err.message });
                        });
                });
            } else if (Array.isArray(csvData)) {
                // New JSON array format
                const results: any[] = [];
                const errors: string[] = [];

                csvData.forEach((row, index) => {
                    const rowIndex = index + 1;
                    try {
                        // Validate and transform the row data
                        const candidateData = validateAndTransformCsvRow(row, rowIndex);
                        if (candidateData.errors.length > 0) {
                            errors.push(...candidateData.errors);
                        } else {
                            // Add user context for multi-tenant isolation
                            candidateData.candidate.createdBy = user.id.toString();
                            results.push(candidateData.candidate);
                        }
                    } catch (err: any) {
                        errors.push(`Row ${rowIndex}: ${err.message}`);
                    }
                });

                if (errors.length > 0) {
                    return {
                        error: "Data validation failed",
                        details: errors,
                        validRows: results.length,
                        totalErrors: errors.length
                    };
                }

                if (results.length === 0) {
                    return { error: "No valid candidates found in data" };
                }

                try {
                    // Batch insert candidates
                    const insertedCandidates = await Candidate.insertMany(results, {
                        ordered: false // Continue inserting even if some fail
                    });

                    return {
                        message: "Data upload successful",
                        inserted: insertedCandidates.length,
                        candidates: insertedCandidates
                    };
                } catch (insertErr: any) {
                    return {
                        error: "Error inserting candidates",
                        details: insertErr?.message || String(insertErr),
                        validatedRows: results.length
                    };
                }
            } else {
                return { error: "CSV data must be either a string or an array of objects" };
            }
        } catch (err: any) {
            return { error: "Error processing CSV upload", details: err?.message || String(err) };
        }
    }, {
        body: t.Object({
            csvData: t.Union([t.String(), t.Array(t.Any())])
        })
    })
    .get("/analytics", async () => {
        try {
            const totalCandidates = await Candidate.countDocuments();
            const candidatesByStage = await Candidate.aggregate([
                { $group: { _id: "$stage", count: { $sum: 1 } } },
            ]);
            return { totalCandidates, candidatesByStage };
        } catch (err: any) {
            return { error: "Error fetching candidate analytics", details: err?.message || String(err) };
        }
    })

