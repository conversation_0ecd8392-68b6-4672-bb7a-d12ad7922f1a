import Elysia from "elysia";
import { Recruiter } from "../models/recruiter.model";
import Job from "../models/job.model";
import { ensureAuth } from "../services/auth";
import Candidate from "../models/candidate.model";
import type { IJob } from "../types/job.types";

export const recruiterRoute = new Elysia({ prefix: "/recruiter" })
    .use(ensureAuth)
    .get("/", async() => {
	    try{
           const recruiters = await Recruiter.find({});
           if(!recruiters){
               return [];
           }
           return recruiters || [];
        }catch(err : any){
            return {error: "There was an error fetching recruiters"};
        }
    })
    .get("/jobs", async ({ user }: { user: { _id: string } }) => {
        try {
            const jobs = await Job.find({ createdBy: user._id });
            return jobs;
        } catch (err: any) {
            return { error: "There was an error fetching jobs" };
        }
    })
    .post("/jobs", async ({ body, user }: { body: Partial<IJob>, user: { _id: string } }) => {
        try {
            const newJob = await Job.create({ ...body, createdBy: user._id });
            return newJob;
        } catch (err: any) {
            return { error: "There was an error creating the job" };
        }
    })
    .get("/jobs/:jobId", async ({ params: { jobId } }) => {
        try {
            const job = await Job.findById(jobId);
            if (!job) {
                return { error: "Job not found" };
            }
            return job;
        } catch (err: any) {
            return { error: "There was an error fetching the job" };
        }
    })
    .put("/jobs/:jobId", async ({ params: { jobId }, body }: { params: { jobId: string }, body: Partial<IJob> }) => {
        try {
            const updatedJob = await Job.findByIdAndUpdate(jobId, body, { new: true, runValidators: true });
            if (!updatedJob) {
                return { error: "Job not found or update failed" };
            }
            return updatedJob;
        } catch (err: any) {
            return { error: "There was an error updating the job" };
        }
    })
    .delete("/jobs/:jobId", async ({ params: { jobId } }) => {
        try {
            const deletedJob = await Job.findByIdAndDelete(jobId);
            if (!deletedJob) {
                return { error: "Job not found or already deleted" };
            }
            return { deletedJob };
        } catch (err: any) {
            return { error: "There was an error deleting the job" };
        }
    })
    .get("/jobs/:jobId/candidates", async ({ params: { jobId } }) => {
        try {
            const candidates = await Candidate.find({ jobId });
            return candidates;
        } catch (err: any) {
            return { error: "There was an error fetching candidates" };
        }
    })
    .post("/jobs/:jobId/candidates/:candidateId/shortlist", async ({ params: { candidateId } }) => {
        try {
            const updatedCandidate = await Candidate.findByIdAndUpdate(candidateId, { status: "shortlisted" }, { new: true });
            if (!updatedCandidate) {
                return { error: "Candidate not found or update failed" };
            }
            return updatedCandidate;
        } catch (err: any) {
            return { error: "There was an error shortlisting the candidate" };
        }
    })
    .post("/jobs/:jobId/candidates/:candidateId/reject", async ({ params: { candidateId } }) => {
        try {
            const updatedCandidate = await Candidate.findByIdAndUpdate(candidateId, { status: "rejected" }, { new: true });
            if (!updatedCandidate) {
                return { error: "Candidate not found or update failed" };
            }
            return updatedCandidate;
        } catch (err: any) {
            return { error: "There was an error rejecting the candidate" };
        }
    })
    .get("/analytics", async ({ user }: { user: { _id: string } }) => {
        try {
            const totalJobs = await Job.countDocuments({ createdBy: user._id });
            const totalCandidates = await Candidate.countDocuments({ recruiterId: user._id });
            return { totalJobs, totalCandidates };
        } catch (err: any) {
            return { error: "There was an error fetching analytics" };
        }
    });
