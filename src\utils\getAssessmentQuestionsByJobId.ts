import { Types } from "mongoose";
import { JobConfigModel } from "../models/jobConfig.model"; // adjust path if needed

export interface AssessmentQuestion {
	topic: string;
	questionText: string;
	followUpQuestions: string[];
}

/**
 * Fetches assessment questions for the given job ID from JobConfigModel.
 * @param jobId - The ObjectId string of the job.
 * @returns Array of assessment questions or throws an error if not found.
 */
export const getAssessmentQuestionsByJobId = async (
	jobId: string,
): Promise<AssessmentQuestion[]> => {
	try {
		const jobConfig = await JobConfigModel.findOne({
			jobId: new Types.ObjectId(jobId),
		});

		if (!jobConfig) {
			throw new Error(`JobConfig not found for jobId: ${jobId}`);
		}

		const assessmentStage = jobConfig.stageConfig.find(
			(config) => config.stage === "assessment",
		);

		if (!assessmentStage) {
			throw new Error(
				`Assessment stage not found in job config for jobId: ${jobId}`,
			);
		}

		const questions = assessmentStage.action?.params?.questions as
			| AssessmentQuestion[]
			| undefined;

		if (!questions || !Array.isArray(questions)) {
			throw new Error(
				`No questions found in assessment stage for jobId: ${jobId}`,
			);
		}

		return questions;
	} catch (error) {
		console.error("❌ Error fetching assessment questions:", error);
		throw new Error("Failed to retrieve assessment questions.");
	}
};
