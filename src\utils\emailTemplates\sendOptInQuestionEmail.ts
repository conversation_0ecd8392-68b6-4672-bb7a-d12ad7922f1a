interface SendOptInQuestionEmailParams {
	candidateName: string;
	jobTitle: string;
	companyName: string;
	optInLink: string;
}

export const sendOptInQuestionEmail = ({
	candidateName,
	jobTitle,
	companyName,
	optInLink,
}: SendOptInQuestionEmailParams) => {
	const subject = `Confirm Your Interest in ${jobTitle} at ${companyName}`;

	const greeting = `Hi ${candidateName},`;

	const textContent = `${greeting}

We’re excited to consider you for the position of ${jobTitle} at ${companyName}.

Before we proceed, please confirm your interest by answering a short question using the link below:
${optInLink}

📌 This helps us ensure you're available and interested before moving forward in the process.

Looking forward to hearing from you.

Warm regards,
Team ${companyName}`;

	return {
		subject,
		textContent,
	};
};
