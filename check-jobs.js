import mongoose from "mongoose";
import Job from "./src/models/job.model.ts";

// Connect to MongoDB
await mongoose.connect("mongodb://localhost:27017/veda");

try {
    const jobs = await Job.find({});
    console.log("Found jobs:", jobs.length);
    
    if (jobs.length > 0) {
        console.log("Available Job IDs:");
        jobs.forEach((job, index) => {
            console.log(`${index + 1}. ID: ${job._id} | Title: ${job.title} | Department: ${job.department}`);
        });
    } else {
        console.log("No jobs found in database. You need to seed some jobs first.");
    }
} catch (error) {
    console.error("Error:", error);
} finally {
    await mongoose.disconnect();
}
