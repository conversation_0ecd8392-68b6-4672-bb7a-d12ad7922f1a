// src/types.ts

export interface CandidateScreeningJobData {
	candidateId: string;
	jobConfigId: string; // To load the correct JobConfig
	candidateResumeUrl?: string;
	candidateProfileText?: string; // Or actual text content
	screeningCriteria?: string; // Dynamic criteria from JobConfig
}

export interface InterviewSchedulingJobData {
	candidateId: string;
	jobConfigId: string;
	interviewerAvailability: {
		date: string; // YYYY-MM-DD
		startTime: string; // HH:MM AM/PM e.g., "1:00 PM"
		endTime: string; // HH:MM AM/PM e.g., "4:00 PM"
	};
	screeningOutcomeDetails?: {
		outcome: "pass" | "fail" | "hold";
		notes: string;
	};
	// Potentially add more details needed for scheduling
}

// Add a generic JobOutcome type for agents to return
export interface AgentOutcome {
	status: string; // e.g., 'completed', 'failed', 'requeued'
	nextStageOutcome?: string; // The outcome to drive the JobConfig flow (e.g., 'pass', 'fail', 'best')
	data?: Record<string, any>; // Any data to pass along (e.g., scheduled time, screening notes)
	error?: string;
}
