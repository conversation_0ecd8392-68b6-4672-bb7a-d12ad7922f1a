// src/services/queue.service.ts
import { Queue } from "bullmq";
import { redisConnection } from "../config/redisConnection";

// A cache to store queue instances so we don't create them repeatedly
const queueCache = new Map<string, Queue>();

/**
 * Creates or retrieves a BullMQ Queue instance for a specific stage.
 * @param stageName - The name of the stage, which will be the queue name.
 * @returns A BullMQ Queue instance.
 */
export const getQueue = (stageName: string): Queue => {
	if (!queueCache.has(stageName)) {
		// Create a new queue instance if not in cache
		const newQueue = new Queue(stageName, { connection: redisConnection });
		queueCache.set(stageName, newQueue);
	}
	return queueCache.get(stageName)!;
};
