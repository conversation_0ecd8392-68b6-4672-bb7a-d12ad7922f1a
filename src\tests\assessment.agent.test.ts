import { assessmentAgent } from "../agents/assessment.agent";
import { connectToDatabase } from "../config/mongodb";

await connectToDatabase();

// Dummy payload for AI Interview assessment
const aiInterviewPayload = {
	questions: [
		{
			topic: "JavaScript Fundamentals",
			questionText:
				"Explain the difference between let, const, and var in JavaScript.",
			followUpQuestions: [
				"Can you provide an example where using const would be preferred over let?",
				"What happens when you try to reassign a const variable?",
			],
		},
		{
			topic: "React Concepts",
			questionText: "What are React hooks and why were they introduced?",
			followUpQuestions: [
				"Can you explain the useState hook with an example?",
				"What are the rules of hooks in React?",
			],
		},
		{
			topic: "Problem Solving",
			questionText: "How would you optimize a slow-performing web application?",
			followUpQuestions: [
				"What tools would you use to identify performance bottlenecks?",
				"Can you explain lazy loading and its benefits?",
			],
		},
	],
	isProctoringRequired: true,
	assessmentType: "ai-interview",
	assessmentDetails: {
		assessmentPlatform: "",
		assessmentLink: "",
	},
};

// Dummy payload for Platform assessment
const platformAssessmentPayload = {
	questions: [],
	isProctoringRequired: false,
	assessmentType: "platform-assessment",
	assessmentDetails: {
		assessmentPlatform: "HackerRank",
		assessmentLink: "https://hackerrank.com/test/assessment-link-123",
	},
};

// Dummy candidate and job IDs
const dummyCandidateId = "68837142cfab6d9676f8a85d";
const dummyJobId = "688355938e98c28d583d022a";
// Test function for AI Interview
async function testAIInterviewAssessment() {
	console.log("Testing AI Interview Assessment...");
	try {
		await assessmentAgent(aiInterviewPayload, dummyCandidateId, dummyJobId);
		console.log("AI Interview assessment test completed successfully");
	} catch (error) {
		console.error("AI Interview assessment test failed:", error);
	}
}

// Test function for Platform Assessment
async function testPlatformAssessment() {
	console.log("Testing Platform Assessment...");
	try {
		await assessmentAgent(
			platformAssessmentPayload,
			dummyCandidateId,
			dummyJobId,
		);
		console.log("Platform assessment test completed successfully");
	} catch (error) {
		console.error("Platform assessment test failed:", error);
	}
}

// Run tests
async function runTests() {
	console.log("Starting Assessment Agent Tests...");

	await testAIInterviewAssessment();
	console.log("---");
	// await testPlatformAssessment();

	console.log("All tests completed!");
}

// Execute tests
runTests();
