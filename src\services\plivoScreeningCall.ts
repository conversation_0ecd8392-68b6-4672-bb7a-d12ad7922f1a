import { env } from "../config/env";

export interface PlivoScreeningCallData {
	questions: string;
	companyName: string;
	role: string;
	to: string;
	webhook_endpoint: string;
	[key: string]: string | number | boolean | undefined; // Allow additional custom fields
}

export interface PlivoScreeningCallResponse {
	success: boolean;
	callId?: string;
	message?: string;
	error?: string;
}

class PlivoScreeningCallService {
	private readonly accountId: string;
	private readonly flowId: string;
	private readonly baseUrl: string;

	constructor() {
		this.accountId = env.PLIVO_ACCOUNT_ID || "";
		this.flowId = env.PLIVO_SCREENING_FLOW_ID || "";
		this.baseUrl = "https://agentflow.plivo.com/v1";

		if (!this.accountId || !this.flowId) {
			console.warn(
				"Plivo screening call credentials not configured. Set PLIVO_ACCOUNT_ID and PLIVO_SCREENING_FLOW_ID in environment variables.",
			);
		}
	}

	/**
	 * Initiates a phone call to send screening questions through Plivo's Agent Flow API
	 */
	async initiateScreeningCall(
		callData: PlivoScreeningCallData,
	): Promise<PlivoScreeningCallResponse> {
		try {
			if (!this.accountId || !this.flowId) {
				throw new Error("Plivo screening call credentials not configured");
			}

			if (!callData.to) {
				throw new Error("Phone number (to) is required");
			}

			if (!callData.questions) {
				throw new Error("Questions are required");
			}

			if (!callData.companyName) {
				throw new Error("Company name is required");
			}

			if (!callData.role) {
				throw new Error("Role is required");
			}

			if (!callData.webhook_endpoint) {
				throw new Error("Webhook URL is required");
			}

			const url = `${this.baseUrl}/account/${this.accountId}/flow/${this.flowId}`;

			console.log(
				`Initiating Plivo screening call to ${callData.to} for company: ${callData.companyName}`,
			);

			const response = await fetch(url, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(callData),
			});

			const responseData: any = await response.json();
			console.log("responseData", responseData);

			// Check for HTTP errors first
			if (!response.ok) {
				console.error("Plivo screening API error:", {
					status: response.status,
					statusText: response.statusText,
					data: responseData,
					phoneNumber: callData.to,
				});

				return {
					success: false,
					error: `Plivo screening API error: ${response.status} - ${
						responseData.message || response.statusText
					}`,
				};
			}

			// Check if the call actually failed despite successful HTTP response
			const isCallFailed =
				responseData.message &&
				(responseData.message.includes("Call failed") ||
					responseData.message.includes("output state: failed") ||
					responseData.message.includes("stopped. next node uuid not found"));

			if (isCallFailed) {
				console.error("Plivo screening call failed:", {
					message: responseData.message,
					phoneNumber: callData.to,
				});

				return {
					success: false,
					error: `Plivo screening call failed: ${responseData.message}`,
				};
			}

			console.log(
				`Plivo screening call initiated successfully to ${callData.to}`,
				{
					callId: responseData.call_uuid || responseData.id,
					phoneNumber: callData.to,
				},
			);

			return {
				success: true,
				callId: responseData.api_id,
				message: "Screening call initiated successfully",
			};
		} catch (error) {
			console.error("Error initiating Plivo screening call:", {
				error: error instanceof Error ? error.message : String(error),
				phoneNumber: callData.to,
			});

			return {
				success: false,
				error:
					error instanceof Error ? error.message : "Unknown error occurred",
			};
		}
	}

	/**
	 * Validates phone number format (basic validation)
	 */
	isValidPhoneNumber(phoneNumber: string): boolean {
		// Basic validation for international format
		const phoneRegex = /^\+?[1-9]\d{1,14}$/;
		return phoneRegex.test(phoneNumber.replace(/\s+/g, ""));
	}

	/**
	 * Formats phone number to ensure it starts with +
	 */
	formatPhoneNumber(phoneNumber: string): string {
		const cleaned = phoneNumber.replace(/\s+/g, "");
		return cleaned.startsWith("+") ? cleaned : `+${cleaned}`;
	}

	/**
	 * Validates all required fields for screening call
	 */
	validateScreeningCallData(data: PlivoScreeningCallData): {
		isValid: boolean;
		errors: string[];
	} {
		const errors: string[] = [];

		if (!data.to) {
			errors.push("Phone number (to) is required");
		} else if (!this.isValidPhoneNumber(data.to)) {
			errors.push("Invalid phone number format");
		}

		if (!data.questions) {
			errors.push("Questions are required");
		}

		if (!data.companyName) {
			errors.push("Company name is required");
		}

		if (!data.role) {
			errors.push("Role is required");
		}

		if (!data.webhook_endpoint) {
			errors.push("Webhook URL is required");
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}
}

export const plivoScreeningCallService = new PlivoScreeningCallService();
