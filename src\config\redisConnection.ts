// src/queues/redisConnection.ts
import { Redis } from "ioredis";

// Use a shared connection for all queues and workers
export const redisConnection = new Redis({
	host: process.env.REDIS_HOST || "localhost",
	port: parseInt(process.env.REDIS_PORT || "6379", 10),
	maxRetriesPerRequest: null, // Disable retries to prevent connection issues on startup
});

redisConnection.on("connect", () => {
	console.log("Redis connected successfully!");
});

redisConnection.on("error", (err) => {
	console.error("Redis connection error:", err);
	// You might want to gracefully exit or handle this more robustly in production
});
