import mongoose from "mongoose";
import { env } from "./env";

export const connectToDatabase = async () => {
	try {
		console.log("Connecting to MongoDB via Mongoose");
		await mongoose.connect(env.MONGO_URI, {
			dbName: env.DATABASE_NAME,
		});
		console.log("Connected to MongoDB via Mongoose");
		return mongoose.connection.db;
	} catch (error) {
		console.error("Error connecting to MongoDB:", error);
		process.exit(1);
	}
};

export const closeDatabaseConnection = async () => {
	try {
		await mongoose.connection.close();
		console.log("MongoDB connection closed");
	} catch (error) {
		console.error("Error closing MongoDB connection:", error);
	}
};
