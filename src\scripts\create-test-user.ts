import { connectToDatabase, closeDatabaseConnection } from "../config/mongodb";
import { <PERSON><PERSON>ruit<PERSON> } from "../models/recruiter.model";
import { Organization } from "../models/organization.model";

async function createTestUser() {
    try {
        await connectToDatabase();
        console.log("Connected to database");

        // First, create a test organization
        let organization = await Organization.findOne({ domain: "testcompany.com" });
        if (!organization) {
            organization = await Organization.create({
                name: "Test Company",
                domain: "testcompany.com",
                address: "123 Test Street, Test City"
            });
            console.log("Created test organization:", organization.name);
        } else {
            console.log("Using existing organization:", organization.name);
        }

        // Check if test user already exists
        const existingUser = await Recruiter.findOne({ email: "<EMAIL>" });
        if (existingUser) {
            console.log("Test user already exists:");
            console.log("Email: <EMAIL>");
            console.log("Password: password123");
            return;
        }

        // Create a test recruiter
        const testRecruiter = await <PERSON>cruiter.create({
            name: "Test Recruiter",
            email: "<EMAIL>",
            password: "password123", // Note: In production, this should be hashed
            organization: organization._id
        });

        console.log("✅ Test user created successfully!");
        console.log("📧 Email: <EMAIL>");
        console.log("🔑 Password: password123");
        console.log("🏢 Organization:", organization.name);
        console.log("\nYou can now use these credentials to login!");

    } catch (error) {
        console.error("❌ Error creating test user:", error);
    } finally {
        await closeDatabaseConnection();
        process.exit(0);
    }
}

createTestUser();
