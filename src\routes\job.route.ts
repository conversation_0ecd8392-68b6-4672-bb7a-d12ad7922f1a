import Elysia, { t } from "elysia";
import Candidate from "../models/candidate.model";
import { fetchExternalCandidates } from "../services/mockExternalATS.service";
import { startJobWorkflow } from "../services/workFlow.service";
import Job from "../models/job.model"; 
import type { IJob } from "../types/job.types";

export const jobRoutes = new Elysia({ prefix: "/job" })
	.get(
		"/jobs/:jobId/candidates",
		async ({ params, set }) => {
			const { jobId } = params;
			console.log(`Received request for job ID: ${jobId} (from local DB)`);

			// Set JSON content type
			set.headers["Content-Type"] = "application/json";

			try {
				const candidates = await Candidate.find({}).select(
					"-contactInfo.email",
				);

				if (candidates.length === 0) {
					set.status = 404;
					return {
						message:
							"No lead candidates found for this job ID (or no active candidates in system).",
					};
				}

				return {
					jobId,
					candidateCount: candidates.length,
					candidates: candidates.map((candidate) => ({
						_id: candidate._id,
						name: candidate.name,
						email: candidate.email,
						currentStage: candidate.stage,
						resumeLink: candidate.resumeLink,
						source: candidate.source,
						expectedSalary: candidate.expectedSalary,
						contactInfo: {
							phone: candidate.contactInfo.phone,
							linkedin: candidate.contactInfo.linkedin,
							github: candidate.contactInfo.github,
							address: candidate.contactInfo.address,
						},
					})),
				};
			} catch (error) {
				console.error("Error fetching candidates:", error);
				set.status = 500;
				return { message: "Internal server error" };
			}
		},
		{
			params: t.Object({
				jobId: t.String(),
			}),
			detail: {
				summary: "Get lead candidates for a specific job ID (from local DB)",
				description:
					"Mimics pulling lead candidates from an ATS for a given job ID. Currently returns all active candidates from local DB as a placeholder.",
				tags: ["Candidates"],
			},
		},
	)
	.get(
		"/external-jobs/:jobId/candidates",
		async ({ params, set }) => {
			const { jobId } = params;
			console.log(
				`Received request for job ID: ${jobId} (from mocked external API)`,
			);

			// Set JSON content type
			set.headers["Content-Type"] = "application/json";

			try {
				// Call the mock external API service
				const externalCandidates = await fetchExternalCandidates(jobId);

				if (externalCandidates.length === 0) {
					set.status = 404;
					return {
						message:
							"No lead candidates found from the mocked external API for this job ID.",
					};
				}

				// Format the response to match your ICandidate structure (if needed)
				const formattedCandidates = externalCandidates.map((candidate) => ({
					_id: candidate._id, // Use the generated mock ID
					name: candidate.name,
					email: candidate.email,
					currentStage: candidate.stage,
					resumeLink: candidate.resumeLink,
					source: candidate.source,
					expectedSalary: candidate.expectedSalary,
					contactInfo: {
						email: candidate.contactInfo?.email || "", // Ensure email is present from external mock
						phone: candidate.contactInfo?.phone,
						linkedin: candidate.contactInfo?.linkedin,
						github: candidate.contactInfo?.github,
						address: candidate.contactInfo?.address,
					},
				}));

				return {
					jobId,
					candidateCount: formattedCandidates.length,
					candidates: formattedCandidates,
				};
			} catch (error) {
				console.error(
					"Error fetching candidates from mocked external API:",
					error,
				);
				set.status = 500;
				return {
					message: "Internal server error when fetching from external API.",
				};
			}
		},
		{
			params: t.Object({
				jobId: t.String(),
			}),
			detail: {
				summary:
					"Get lead candidates for a specific job ID (from Mocked External API)",
				description:
					"Mimics pulling lead candidates from an external ATS for a given job ID using a mock service. Returns at least 10 candidates.",
				tags: ["External Candidates"],
			},
		},
	)
	.post(
		"/workflows/:jobId/start",
		async ({ params, set }) => {
			const { jobId } = params;
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			return await startJobWorkflow(jobId);
		},
		{
			params: t.Object({
				jobId: t.String(),
			}),
		},
	)
	.post(
		"/",
		async ({ body, set }: { body: IJob, set: any }) => {
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			const {
				title,
				description,
				department,
				location,
				jobType,
				workLocation,
				experienceLevel,
				requiredSkills,
				qualifications,
				responsibilities,
				salaryRange,
				status,
				openings,
				applicationDeadline,
				startDate,
				createdBy,
				organization,
			} = body;
			console.log("this is the body:" , body)
			if (!title || !description || !department || !location || !jobType || !workLocation || !experienceLevel || !status || !openings || !createdBy || !organization) {
				return { error: "Missing required fields" };
			}

			try {
				const newJob = await Job.create({
					title,
					description,
					department,
					location,
					jobType,
					workLocation,
					experienceLevel,
					requiredSkills,
					qualifications,
					responsibilities,
					salaryRange,
					status,
					openings,
					applicationDeadline,
					startDate,
					createdBy,
					organization,
				});
				return newJob;
			} catch (error: any) {
				return { error: "Error creating job", details: error?.message || String(error) };
			}
		},
	)
	.get(
		"/",
		async ({ set }) => {
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			try {
				const jobs = await Job.find({});
				return jobs;
			} catch (error: any) {
				return { error: "Error fetching jobs", details: error?.message || String(error) };
			}
		},
	)
	.get(
		"/:jobId",
		async ({ params: { jobId }, set }) => {
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			if (!jobId) {
				return { error: "Job ID is required" };
			}
			try {
				const job = await Job.findById(jobId);
				if (!job) {
					return { error: "Job not found" };
				}
				return job;
			} catch (error: any) {
				return { error: "Error fetching job", details: error?.message || String(error) };
			}
		},
	)
	.put(
		"/:jobId",
		async ({ params: { jobId }, body, set }: { params: { jobId: string }, body: Partial<IJob>, set: any }) => {
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			if (!jobId) {
				return { error: "Job ID is required" };
			}

			try {
				const updatedJob = await Job.findByIdAndUpdate(jobId, body, {
					new: true,
					runValidators: true,
				});
				if (!updatedJob) {
					return { error: "Job not found or update failed" };
				}
				return updatedJob;
			} catch (error: any) {
				return { error: "Error updating job", details: error?.message || String(error) };
			}
		},
	)
	.delete(
		"/:jobId",
		async ({ params: { jobId }, set }) => {
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			if (!jobId) {
				return { error: "Job ID is required" };
			}
			try {
				const deletedJob = await Job.findByIdAndDelete(jobId);
				if (!deletedJob) {
					return { error: "Job not found or already deleted" };
				}
				return { deletedJob };
			} catch (error: any) {
				return { error: "Error deleting job", details: error?.message || String(error) };
			}
		},
	)
	.get(
		"/analytics",
		async ({ set }) => {
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			try {
				const totalJobs = await Job.countDocuments();
				return { totalJobs };
			} catch (error: any) {
				return { error: "Error fetching analytics", details: error?.message || String(error) };
			}
		},
	)
	.get(
		"/search",
		async ({ query, set }) => {
			// Set JSON content type
			set.headers["Content-Type"] = "application/json";
			const searchCriteria: Record<string, any> = {};
			if (query.title) searchCriteria.title = { $regex: query.title, $options: "i" };
			if (query.location) searchCriteria.location = { $regex: query.location, $options: "i" };
			try {
				const jobs = await Job.find(searchCriteria);
				return jobs;
			} catch (error: any) {
				return { error: "Error searching jobs", details: error?.message || String(error) };
			}
		},
	);
