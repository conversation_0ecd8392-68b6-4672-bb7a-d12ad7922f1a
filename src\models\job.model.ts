import { model, Schema, type Types } from "mongoose";

// Enum for job status
export enum JobStatus {
	DRAFT = "draft",
	ACTIVE = "active",
	PAUSED = "paused",
	CLOSED = "closed",
	CANCELLED = "cancelled",
}

// Enum for job type
export enum JobType {
	FULL_TIME = "full_time",
	PART_TIME = "part_time",
	CONTRACT = "contract",
	INTERNSHIP = "internship",
	FREELANCE = "freelance",
}

// Enum for work location
export enum WorkLocation {
	REMOTE = "remote",
	ONSITE = "onsite",
	HYBRID = "hybrid",
}

// Enum for experience level
export enum ExperienceLevel {
	ENTRY = "entry",
	JUNIOR = "junior",
	MID = "mid",
	SENIOR = "senior",
	LEAD = "lead",
	EXECUTIVE = "executive",
}

// Interface for salary range
export interface SalaryRange {
	min: number;
	max: number;
	currency: string;
	period: "hourly" | "monthly" | "yearly";
}

// Interface for required skills
export interface RequiredSkill {
	name: string;
	level: "nice_to_have" | "required" | "preferred";
	yearsRequired?: number;
}

// Main Job interface
export interface IJob {
	title: string;
	description: string;
	department: string;
	location: string;
	jobType: JobType;
	workLocation: WorkLocation;
	experienceLevel: ExperienceLevel;
	requiredSkills: RequiredSkill[];
	qualifications: string[];
	responsibilities: string[];
	salaryRange?: SalaryRange;
	status: JobStatus;
	openings: number;
	applicationDeadline?: Date;
	startDate?: Date;
	postedDate: Date;
	createdAt: Date;
	updatedAt: Date;
	createdBy: Types.ObjectId;
	organization: Types.ObjectId;
}

// Mongoose Schema
const jobSchema = new Schema<IJob>(
	{
		title: {
			type: String,
			required: true,
			trim: true,
			maxlength: 200,
		},
		description: {
			type: String,
			required: true,
			trim: true,
			maxlength: 5000,
		},
		department: {
			type: String,
			required: true,
			trim: true,
			maxlength: 100,
		},
		location: {
			type: String,
			required: true,
			trim: true,
			maxlength: 200,
		},
		jobType: {
			type: String,
			enum: Object.values(JobType),
			required: true,
		},
		workLocation: {
			type: String,
			enum: Object.values(WorkLocation),
			required: true,
		},
		experienceLevel: {
			type: String,
			enum: Object.values(ExperienceLevel),
			required: true,
		},
		requiredSkills: [
			{
				name: {
					type: String,
					required: true,
					trim: true,
				},
				level: {
					type: String,
					enum: ["nice_to_have", "required", "preferred"],
					required: true,
				},
				yearsRequired: {
					type: Number,
					min: 0,
					max: 20,
				},
			},
		],
		qualifications: [
			{
				type: String,
				trim: true,
				maxlength: 500,
			},
		],
		responsibilities: [
			{
				type: String,
				trim: true,
				maxlength: 500,
			},
		],
		salaryRange: {
			min: {
				type: Number,
				min: 0,
			},
			max: {
				type: Number,
				min: 0,
			},
			currency: {
				type: String,
				default: "USD",
				trim: true,
				uppercase: true,
				maxlength: 3,
			},
			period: {
				type: String,
				enum: ["hourly", "monthly", "yearly"],
				default: "yearly",
			},
		},
		status: {
			type: String,
			enum: Object.values(JobStatus),
			default: JobStatus.DRAFT,
			required: true,
		},
		openings: {
			type: Number,
			required: true,
			min: 1,
			default: 1,
		},
		applicationDeadline: {
			type: Date,
		},
		startDate: {
			type: Date,
		},
		postedDate: {
			type: Date,
			default: Date.now,
			required: true,
		},
		createdBy: {
			type: Schema.Types.ObjectId,
			ref: "Recruiter",
			required: true,
		},
		organization: {
			type: Schema.Types.ObjectId,
			ref: "Organization",
			required: true,
		},
	},
	{
		timestamps: true,
	},
);

const Job = model<IJob>("Job", jobSchema);

export default Job;
