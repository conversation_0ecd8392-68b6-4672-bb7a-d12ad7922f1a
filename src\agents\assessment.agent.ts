import axios from "axios";
import { env } from "../config/env";
import Candidate from "../models/candidate.model";
import Job from "../models/job.model";
import { Organization } from "../models/organization.model";
import { Recruiter } from "../models/recruiter.model";
import { sendEmail } from "../services/email.service";
import { sendAIInterviewEmail } from "../utils/emailTemplates/sendAIInterviewEmail";
import { sendPlatformAssessmentEmail } from "../utils/emailTemplates/sendPlatformAssessmentEmail";

interface AssessmentParams {
	questions: Array<{
		topic: string;
		questionText: string;
		followUpQuestions: string[];
	}>;
	isProctoringRequired: boolean;
	assessmentType: string;
	assessmentDetails: {
		assessmentPlatform: string;
		assessmentLink: string;
	};
}

/**
 * Performs the automated assessment logic.
 * @param params The specific parameters for this agent.
 * @param jobId The ID of the job.
 * @param candidateId The ID of the candidate.
 * @returns An outcome: "pass" or "fail".
 */
export const assessmentAgent = async (
	params: AssessmentParams,
	candidateId: string,
	jobId: string,
) => {
	try {
		const candidate = await Candidate.findById(candidateId);
		if (!candidate) {
			throw new Error(`Candidate not found with ID ${candidateId}`);
		}
		const job = await Job.findById(jobId);
		if (!job) {
			throw new Error(`Job not found with ID ${jobId}`);
		}

		const recruiter = await Recruiter.findById(job.createdBy);
		if (!recruiter) {
			console.error(
				`Recruiter or organization not found for job: ${candidate.jobId}`,
			);
			throw new Error("Recruiter or organization not found");
		}

		const organisation = await Organization.findById(recruiter.organization);
		if (!organisation) {
			console.error(`Organization not found for recruiter: ${recruiter._id}`);
			throw new Error("Organization not found");
		}
		const candidateEmail = candidate.email;

		if (params.assessmentType === "ai-interview") {
			const apiUrl = env.API_URL;
			const webhookURL = `${apiUrl}/veda-assessment/ai-interview/result`;

			const payload = {
				questions: params.questions,
				isProctoringRequired: params.isProctoringRequired,
				candidateId,
				redirectUrl: webhookURL,
			};

			const generateInterviewUrl =
				"https://placed.global/api/interview/admin/custom-interview/generate";

			const response = await axios.post(generateInterviewUrl, payload);

			if (!response.data.success) {
				throw new Error(
					`Failed to generate the interview for the candidateId ${candidateId}`,
				);
			}

			const interviewLink = response.data.interviewLink;

			const { subject, textContent } = sendAIInterviewEmail({
				candidateName: candidate.name,
				jobTitle: job.title,
				companyName: organisation.name,
				aiInterviewLink: interviewLink,
			});
			await sendEmail({
				toEmail: candidateEmail,
				subject,
				textContent,
			});
			console.log("ai-interview Assessment sent successfully");
		} else {
			const { subject, textContent } = sendPlatformAssessmentEmail({
				candidateName: candidate.name,
				jobTitle: job.title,
				companyName: organisation.name,
				assessmentPlatform: params.assessmentDetails.assessmentPlatform,
				assessmentLink: params.assessmentDetails.assessmentLink,
			});
			await sendEmail({
				toEmail: candidateEmail,
				subject,
				textContent,
			});
			console.log("Platform Assessment sent successfully");
		}
	} catch (error) {
		console.error("Error in assessmentAgent:", error);
		throw error;
	}
};
