import { Elysia, t } from "elysia";
import Candidate from "../models/candidate.model";
import Job from "../models/job.model";
import { analyzeInterview } from "../services/analyzeInterview";
import { generateAssessmentQuestions } from "../services/generateAssessmentQuestions";
import { webhookLogic } from "../services/handleWebhookLogic";
import { getAssessmentQuestionsByJobId } from "../utils/getAssessmentQuestionsByJobId";

export const vedaAssessmentRoute = new Elysia({
	prefix: "/veda-assessment",
})
	.post(
		"ai-interview/result",
		async ({ body }) => {
			const { candidateId, transcript } = body;

			const candidate = await Candidate.findById(candidateId);
			if (!candidate) throw new Error("Candidate not found");

			const job = await Job.findById(candidate.jobId);
			if (!job) throw new Error("Job not found");

			const jobDetails = `**JOB DETAILS:**
            - **Title:** ${job.title}
            - **Experience Level:** ${job.experienceLevel}
            - **Description:** ${job.description}
            - **Key Responsibilities:**
                - ${job.responsibilities?.join("\n                - ")}
            - **Required Skills:**
                - ${job.requiredSkills?.join("\n                - ")}`;

			const questions = await getAssessmentQuestionsByJobId(job._id.toString());

			const { outcome, reasoning } = await analyzeInterview(
				jobDetails,
				transcript,
				questions,
			);

			console.log(`The analysisResult for the candidate: ${candidateId}`, {
				outcome,
				reasoning,
			});

			await webhookLogic(candidateId, "assessment", outcome, { reasoning });

			return { message: "Veda review webhook processed successfully." };
		},
		{
			body: t.Object({
				candidateId: t.String(),
				transcript: t.Array(
					t.Object({
						type: t.Union([t.Literal("user"), t.Literal("system")]),
						content: t.String(),
					}),
				),
			}),
		},
	)
	.post(
		"manual/result",
		async ({ body }) => {
			const { outcome, candidateId } = body;

			console.log(
				`Received 'assessment' webhook for candidate ${candidateId} with outcome: ${outcome}`,
			);

			const reasoning =
				outcome === "qualified"
					? "candidate has passed the assessment and this is updated manually"
					: "candidate has failed the assessment and this is updated manually";

			await webhookLogic(candidateId, "assessment", outcome, { reasoning });

			return { message: "Veda review webhook processed successfully." };
		},
		{
			body: t.Object({
				outcome: t.String(),
				candidateId: t.String(),
			}),
		},
	)
	.post(
		"/get-interview-questions",
		async ({ body }) => {
			try {
				const { skills, numberOfQuestions } = body;

				// Validate input
				if (!skills || skills.length === 0) {
					return {
						success: false,
						error: "At least one skill must be provided",
					};
				}

				if (numberOfQuestions <= 0 || numberOfQuestions > 50) {
					return {
						success: false,
						error: "Number of questions must be between 1 and 50",
					};
				}

				const questions = await generateAssessmentQuestions(
					skills,
					numberOfQuestions,
				);

				if (!questions || questions.length === 0) {
					return {
						success: false,
						error: "Failed to generate questions",
					};
				}

				return { success: true, questions };
			} catch (error) {
				console.error("Error generating interview questions:", error);
				return {
					success: false,
					error: "Internal server error while generating questions",
				};
			}
		},
		{
			body: t.Object({
				skills: t.Array(t.String(), { minItems: 1 }),
				numberOfQuestions: t.Number({ minimum: 1, maximum: 50 }),
			}),
		},
	);
