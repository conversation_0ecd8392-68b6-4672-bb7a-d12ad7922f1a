import mongoose, { type Document, Schema, type Types } from "mongoose";

export enum ICommunicationChannel {
	EMAIL = "EMAIL",
	PLIVO = "PLIVO",
	WHATSAPP = "WHATSAPP",
	SLACK = "SLACK",
	CALENDAR = "CALENDAR",
}

// Mongoose Document interfaces
export interface INextStatusConfig {
	stage: string;
	outcome: string;
}

export interface IStatusConfig {
	stage: string;
	next: INextStatusConfig[];
}

export interface IStageAction {
	agentId: string;
	outputs?: string[];
	params: Record<string, unknown>;
}

// NEW: Define the scheduling configuration interface
export interface IStageScheduling {
	type: "IMMEDIATE" | "BUSINESS_HOURS"; // Add more types like 'DAILY_AT' as needed
	params?: {
		timezone?: string; // e.g., 'Asia/Kolkata'
		startHour?: number; // e.g., 10
		endHour?: number; // e.g., 20
	};
}

export interface IStageConfig {
	stage: string;
	scheduling?: IStageScheduling; // <-- ADD THIS NEW PROPERTY
	action: IStageAction;
	communicationChannel?: ICommunicationChannel;
}

export interface IJobConfig extends Document {
	jobId: Types.ObjectId;
	flow: IStatusConfig[];
	stageConfig: IStageConfig[];
}

// Mongoose Schemas
const NextStatusConfigSchema = new Schema<INextStatusConfig>({
	stage: { type: String, required: true },
	outcome: { type: String, required: true },
});

const StatusConfigSchema = new Schema<IStatusConfig>({
	stage: { type: String, required: true },
	next: [NextStatusConfigSchema],
});

const StageActionSchema = new Schema<IStageAction>({
	agentId: { type: String, required: true },
	outputs: [{ type: String, required: true }],
	params: { type: Schema.Types.Mixed, required: true },
});

const StageSchedulingSchema = new Schema<IStageScheduling>(
	{
		type: {
			type: String,
			required: true,
			enum: ["IMMEDIATE", "BUSINESS_HOURS"],
		},
		params: {
			timezone: String,
			startHour: Number,
			endHour: Number,
		},
	},
	{ _id: false },
);

const StageConfigSchema = new Schema<IStageConfig>({
	stage: { type: String, required: true },
	action: { type: StageActionSchema, required: true },
	scheduling: { type: StageSchedulingSchema, required: false },
	communicationChannel: {
		type: String,
		enum: Object.values(ICommunicationChannel),
		required: false,
	},
});

const JobConfigSchema = new Schema<IJobConfig>(
	{
		jobId: { type: Schema.Types.ObjectId, ref: "Job", required: true },
		flow: [StatusConfigSchema],
		stageConfig: [StageConfigSchema],
	},
	{
		timestamps: true,
	},
);

// Create and export the model
export const JobConfigModel = mongoose.model<IJobConfig>(
	"JobConfig",
	JobConfigSchema,
);
