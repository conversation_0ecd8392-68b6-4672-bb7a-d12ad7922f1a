import { openaiClient } from "../config/openai";
import type { AssessmentQuestion } from "./analyzeInterview";

// Define the structure for the JSON response we expect from the AI
interface QuestionGenerationResult {
	questions: AssessmentQuestion[];
}

/**
 * Generates a specified number of interview questions based on a list of skills.
 *
 * @param skills An array of strings representing the skills to generate questions for.
 * @param numberOfQuestions The total number of questions to generate.
 * @returns A promise that resolves to an array of AssessmentQuestion objects.
 */
export const generateAssessmentQuestions = async (
	skills: string[],
	numberOfQuestions: number,
): Promise<AssessmentQuestion[]> => {
	const skillsList = skills.join(", ");

	// 1. Construct a precise system prompt to guide the AI's question generation process.
	const systemPrompt = `
        You are an expert hiring manager and curriculum designer for both technical and non-technical roles.
        Your task is to generate insightful interview questions based on a list of required skills.

        For each question, provide:
        - A "topic" which is the skill being assessed.
        - A main "questionText" to evaluate the candidate's proficiency.
        - An array of "followUpQuestions" to probe deeper into their knowledge and experience. This array can be empty.

        You MUST respond with a single JSON object containing exactly one key: "questions".
        The value of "questions" must be an array of question objects, where each object strictly follows the structure: { "topic": string, "questionText": string, "followUpQuestions": string[] }.

        Do not include any other text, markdown, or explanations outside of this JSON object.
    `;

	// 2. Construct the user prompt with the specific requirements for this request.
	const userPrompt = `
        Please generate exactly ${numberOfQuestions} interview questions covering the following skills: ${skillsList}.
        Distribute the questions logically across the provided skills.
    `;

	try {
		// 3. Call the OpenAI Chat Completions API.
		const response = await openaiClient.chat.completions.create({
			model: "gpt-4o",
			response_format: { type: "json_object" },
			messages: [
				{ role: "system", content: systemPrompt },
				{ role: "user", content: userPrompt },
			],
			temperature: 0.5, // A slightly higher temperature for more creative and varied questions.
		});

		const resultString = response.choices[0]?.message?.content;

		if (!resultString) {
			throw new Error("OpenAI API returned an empty response.");
		}

		// 4. Parse and validate the JSON response.
		const generationResult: QuestionGenerationResult = JSON.parse(resultString);

		if (
			!generationResult.questions ||
			!Array.isArray(generationResult.questions)
		) {
			throw new Error(
				"Invalid JSON structure received from OpenAI. 'questions' array is missing.",
			);
		}

		// You could add more detailed validation here if needed, checking each object in the array.

		return generationResult.questions;
	} catch (error) {
		console.error("Error during question generation:", error);
		// In case of an error, return an empty array or throw a custom error.
		throw new Error("Failed to generate interview questions.");
	}
};
