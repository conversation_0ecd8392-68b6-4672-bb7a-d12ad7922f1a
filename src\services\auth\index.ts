import Elysia from "elysia";
import { Recruiter } from "../../models/recruiter.model";

export const ensureAuth = new Elysia({ name: "Auth.Service" }).derive(
	{ as: "global" },
	async ({ cookie, status }) => {
		if (!cookie.authToken?.value) return status(401);
		const user = await Recruiter.findByToken(cookie.authToken.value);
		if (!user) return status(401);
		return {
			user: {
				id: user._id,
				organization: user.organization,
				email: user.email,
				name: user.name,
			},
		};
	},
);
