import Candidate from "../models/candidate.model";
import { triggerNextStage } from "./workflow.orchestrator"; // <-- Import the new orchestrator

/**
 * Starts the workflow for a given hiring job.
 * Loops through all candidates and calls the orchestrator to queue them for the first stage.
 * @param jobId - The ID of the hiring job to start.
 */
export const startJobWorkflow = async (jobId: string) => {
	const candidates = await Candidate.find({ jobId, status: "registered" })
		.select("_id")
		.lean();

	if (candidates.length === 0) {
		console.log(`No registered candidates found for job ${jobId}.`);
		return { message: "No new candidates to process." };
	}

	const promises = candidates.map((candidate) =>
		triggerNextStage(candidate._id.toString(), "START"),
	);

	await Promise.all(promises);

	return {
		message: `Successfully triggered workflow for ${candidates.length} candidates.`,
	};
};
