interface PlatformAssessmentEmailParams {
	candidateName: string;
	jobTitle: string;
	companyName: string;
	assessmentPlatform: string;
	assessmentLink: string;
}

export const sendPlatformAssessmentEmail = ({
	candidateName,
	jobTitle,
	companyName,
	assessmentPlatform,
	assessmentLink,
}: PlatformAssessmentEmailParams) => {
	const subject = `Your ${assessmentPlatform} Assessment for ${jobTitle} at ${companyName}`;

	const greeting = `Hi ${candidateName},`;

	const textContent = `${greeting}

        You're invited to complete an assessment on ${assessmentPlatform} as part of the hiring process for the role of ${jobTitle} at ${companyName}.

        🧠 The assessment is designed to evaluate your technical and problem-solving skills.
        🕒 You can attempt the test anytime before the deadline (check the assessment page for details).
        📌 Make sure you're in a quiet place with a stable internet connection before starting.

        Click the link below to start the assessment:
        ${assessmentLink}

        If you need any assistance, feel free to contact our support team.

        All the best!
        Team ${companyName}
        `;

	return {
		subject,
		textContent,
	};
};
