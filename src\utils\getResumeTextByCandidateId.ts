import axios from "axios";
import { extractText, getDocumentProxy } from "unpdf";
import Candidate from "../models/candidate.model";

/**
 * Fetches and parses a candidate's resume PDF into plain text.
 * @param candidateId - The ID of the candidate.
 * @returns The plain text extracted from the resume PDF.
 * @throws Error if candidate not found, resume link missing, or PDF processing fails.
 */
export const getResumeTextByCandidateId = async (
	candidateId: string,
): Promise<string> => {
	try {
		// Step 1: Find the candidate in the database
		const candidate = await Candidate.findById(candidateId);
		if (!candidate) {
			console.error(`❌ Candidate with ID ${candidateId} not found.`);
			throw new Error(`Candidate with ID ${candidateId} not found.`);
		}

		if (!candidate.resumeLink) {
			console.warn(`⚠️ Candidate ${candidateId} has no resume link.`);
			throw new Error("Resume link not available.");
		}

		// Step 2: Fetch the resume PDF
		console.log(`📄 Fetching resume from: ${candidate.resumeLink}`);
		const pdfBuffer = await axios.get(candidate.resumeLink, {
			responseType: "arraybuffer",
		});

		// Step 3: Parse the PDF into text
		const bytes = new Uint8Array(pdfBuffer.data);
		const pdf = await getDocumentProxy(bytes);
		const { text } = await extractText(pdf, { mergePages: true });

		console.log("✅ Resume parsed successfully.");
		return text;
	} catch (error) {
		console.error("❌ Error extracting resume text:", error);
		throw new Error("Failed to extract resume text.");
	}
};
