// src/services/mockExternalATS.ts
import { type ICandidate, StageStatus } from "../models/candidate.model";

// A function to generate a random string for sourceUid
export const generateRandomString = (length = 12) => {
	const chars =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
	let result = "";
	for (let i = 0; i < length; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length));
	}
	return result;
};

// A function to simulate fetching candidates from an external ATS
export async function fetchExternalCandidates(
	jobId: string,
): Promise<Partial<ICandidate>[]> {
	console.log(`[Mock External ATS] Fetching candidates for job ID: ${jobId}`);

	// Simulate network delay
	await new Promise((resolve) => setTimeout(resolve, 500)); // 500ms delay

	const mockCandidates: Partial<ICandidate>[] = [
		{
			name: "<PERSON>",
			email: "<EMAIL>",
			phone: "+1112223333",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/d81c9745-63bd-4943-b1d6-a66f4eb36ca8_1749975492299.pdf",
			stage: "applied",
			expectedSalary: 72000,
			contactInfo: { email: "<EMAIL>", phone: "+1112223333" },
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Liam Neeson",
			email: "<EMAIL>",
			phone: "+14445556666",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/33d0ab84-bde0-4fd7-8cfa-43248ac6af1a_1746342022070.pdf",
			stage: "applied",
			expectedSalary: 90000,
			contactInfo: {
				email: "<EMAIL>",
				linkedin: "linkedin.com/in/liam",
			},
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Olivia Rodrigo",
			email: "<EMAIL>",
			phone: "+17778889999",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/918dbeff-280c-4e9a-8e0e-908ccbb0f269_1747968805511.pdf",
			stage: "applied",
			expectedSalary: 65000,
			contactInfo: {
				email: "<EMAIL>",
				github: "github.com/olivia",
			},
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Noah Centineo",
			email: "<EMAIL>",
			phone: "+10102020303",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/df0f9966-0f15-4508-87f8-b4b83c4af0f5_1747044315191.pdf",
			stage: "applied",
			expectedSalary: 78000,
			contactInfo: { email: "<EMAIL>" },
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Ava Max",
			email: "<EMAIL>",
			phone: "+19998887777",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/e03b22bd-0f25-4a07-b96a-185c4911998e_1747802256579.pdf",
			stage: "applied",
			expectedSalary: 82000,
			contactInfo: {
				email: "<EMAIL>",
				linkedin: "linkedin.com/in/avamax",
			},
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "William Smith",
			email: "<EMAIL>",
			phone: "+16665554444",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/14422067-3bd2-402d-8754-341406d5faeb_1744718694159.pdf",
			stage: "applied",
			expectedSalary: 100000,
			contactInfo: { email: "<EMAIL>", phone: "+16665554444" },
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Sophia Brown",
			email: "<EMAIL>",
			phone: "+13332221111",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/749f3ab5-ccff-433c-b84b-7e53b702bb68_1748340530204.pdf",
			stage: "applied",
			expectedSalary: 68000,
			contactInfo: { email: "<EMAIL>" },
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "James Wilson",
			email: "<EMAIL>",
			phone: "+18887776666",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/46e8d7ce-3162-45f1-bdd3-7ed3db55bb11_1745587765130.pdf",
			stage: "applied",
			expectedSalary: 75000,
			contactInfo: { email: "<EMAIL>" },
			status: StageStatus.PENDING_SCHEDULE, // This one is rejected, so might not be a "lead"
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Isabella Garcia",
			email: "<EMAIL>",
			phone: "+15554443333",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/e174d00d-b91a-44fe-9786-65259abdc611_1746086648364.pdf",
			stage: "applied",
			expectedSalary: 92000,
			contactInfo: {
				email: "<EMAIL>",
				github: "github.com/isabella",
			},
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Mason Martinez",
			email: "<EMAIL>",
			phone: "+12221110000",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/e40c3fb0-8faa-4ac7-8783-e8f3ef338277_1746181795026.pdf",
			stage: "applied",
			expectedSalary: 80000,
			contactInfo: { email: "<EMAIL>" },
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
		{
			name: "Harper Lee",
			email: "<EMAIL>",
			phone: "+17770001111",
			resumeLink:
				"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/0bde2e02-81b7-42f5-807e-2ba98fbc1425_1745340193410.pdf",
			stage: "applied",
			expectedSalary: 67000,
			contactInfo: { email: "<EMAIL>" },
			status: StageStatus.PENDING_SCHEDULE,
			source: "manual",
			createdAt: new Date(),
			updatedAt: new Date(),
			sourceUid: generateRandomString(),
		},
	];

	// Filter for 'active' candidates to be considered 'leads'
	const activeLeads = mockCandidates.filter(
		(c) => c.status === StageStatus.PENDING_SCHEDULE,
	);

	return activeLeads;
}
