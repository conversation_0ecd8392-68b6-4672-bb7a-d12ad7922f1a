import mongoose, { Schema, type Types } from "mongoose";

export interface IOrganization {
	_id: Types.ObjectId;
	name: string;
	address?: string;
	createdAt: Date;
	updatedAt: Date;
	domain: string;
}

const OrganizationSchema = new Schema<IOrganization>(
	{
		name: {
			type: String,
			required: true,
			trim: true,
		},
		address: {
			type: String,
			required: false,
			trim: true,
		},
		domain: {
			type: String,
			required: true,
			trim: true,
		},
	},
	{
		timestamps: true,
	},
);

export const Organization = mongoose.model<IOrganization>(
	"Organization",
	OrganizationSchema,
);
