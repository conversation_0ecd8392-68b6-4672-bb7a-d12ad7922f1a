import Elysia from "elysia";
import { JobConfigModel } from "../models/jobConfig.model";
import type { IStageAction, IStageScheduling, INextStatusConfig } from "../models/jobConfig.model";

export const jobConfig = new Elysia({prefix :"/job-config"})
    .get("/", async() => {
        try{
            const jobConfigs = await JobConfigModel.find({})
            if(!jobConfigs){
                return [];
            }
            return jobConfigs;
        }catch(err : any){
            return {error : "something went wrong while fetching job configs"};
        }
    })
    .post("/", async ({ body }: { body: any }) => {
        try {
            const newJobConfig = await JobConfigModel.create(body);
            return newJobConfig;
        } catch (err: any) {
            return { error: "Error creating job configuration", details: err?.message || String(err) };
        }
    })
    .get("/:jobId", async ({ params: { jobId } }: { params: { jobId: string } }) => {
        try {
            const jobConfig = await JobConfigModel.findOne({ jobId });
            if (!jobConfig) {
                return { error: "Job configuration not found" };
            }
            return jobConfig;
        } catch (err: any) {
            return { error: "Error fetching job configuration", details: err?.message || String(err) };
        }
    })
    .put("/:jobId/stage/:stage", async ({ params: { jobId, stage }, body }: { params: { jobId: string; stage: string }; body: { next?: INextStatusConfig[]; scheduling?: IStageScheduling } }) => {
        try {
            const jobConfig = await JobConfigModel.findOne({ jobId });
            if (!jobConfig) {
                return { error: "Job configuration not found" };
            }

            const stageConfig = jobConfig.stageConfig.find((s) => s.stage === stage);
            if (!stageConfig) {
                return { error: "Stage not found in job configuration" };
            }

            // Restrict updates to outputs/outcomes
            if (body.next) {
                const statusConfig = jobConfig.flow.find((f) => f.stage === stage);
                if (!statusConfig) {
                    return { error: "Status configuration not found for the stage" };
                }
                statusConfig.next = body.next; // Update the next field
            }

            if (body.scheduling) {
                stageConfig.scheduling = body.scheduling; // Update scheduling if provided
            }

            await jobConfig.save();
            return jobConfig;
        } catch (err: any) {
            return { error: "Error updating stage action", details: err?.message || String(err) };
        }
    })
    .delete("/:jobId", async ({ params: { jobId } }) => {
        try {
            const deletedJobConfig = await JobConfigModel.findOneAndDelete({ jobId });
            if (!deletedJobConfig) {
                return { error: "Job configuration not found or already deleted" };
            }
            return { deletedJobConfig };
        } catch (err: any) {
            return { error: "Error deleting job configuration", details: err?.message || String(err) };
        }
    })
    .get("/analytics", async () => {
        try {
            const totalJobConfigs = await JobConfigModel.countDocuments();
            const stagesPerJob = await JobConfigModel.aggregate([
                { $unwind: "$stageConfig" },
                { $group: { _id: "$jobId", stageCount: { $sum: 1 } } },
            ]);
            return { totalJobConfigs, stagesPerJob };
        } catch (err: any) {
            return { error: "Error fetching job configuration analytics", details: err?.message || String(err) };
        }
    });
