import Elysia from "elysia";
import { JobConfigModel } from "../models/jobConfig.model";
import type { IStageAction, IStageScheduling, INextStatusConfig } from "../models/jobConfig.model";

export const jobConfig = new Elysia({prefix :"/job-config"})
    .get("/", async() => {
        try{
            const jobConfigs = await JobConfigModel.find({}).lean();
            if(!jobConfigs || jobConfigs.length === 0){
                return {
                    success: true,
                    data: [],
                    message: "No job configurations found"
                };
            }

            // Format the response properly
            const formattedConfigs = jobConfigs.map((config: any) => ({
                _id: config._id,
                jobId: config.jobId,
                flow: config.flow.map((flowItem: any) => ({
                    stage: flowItem.stage,
                    next: flowItem.next.map((nextItem: any) => ({
                        stage: nextItem.stage,
                        outcome: nextItem.outcome
                    }))
                })),
                stageConfig: config.stageConfig.map((stageItem: any) => ({
                    stage: stageItem.stage,
                    action: {
                        agentId: stageItem.action.agentId,
                        outputs: stageItem.action.outputs,
                        params: stageItem.action.params
                    },
                    communicationChannel: stageItem.communicationChannel,
                    scheduling: stageItem.scheduling
                })),
                createdAt: config.createdAt,
                updatedAt: config.updatedAt
            }));

            return {
                success: true,
                data: formattedConfigs,
                count: formattedConfigs.length
            };
        }catch(err : any){
            return {error : "Error fetching job configurations", details: err?.message || String(err)};
        }
    })
    .post("/", async ({ body }: { body: any }) => {
        try {
            const newJobConfig = await JobConfigModel.create(body);
            const configData = newJobConfig.toObject() as any;

            return {
                success: true,
                message: "Job configuration created successfully",
                data: {
                    _id: configData._id,
                    jobId: configData.jobId,
                    flow: configData.flow.map((flowItem: any) => ({
                        stage: flowItem.stage,
                        next: flowItem.next.map((nextItem: any) => ({
                            stage: nextItem.stage,
                            outcome: nextItem.outcome
                        }))
                    })),
                    stageConfig: configData.stageConfig.map((stageItem: any) => ({
                        stage: stageItem.stage,
                        action: {
                            agentId: stageItem.action.agentId,
                            outputs: stageItem.action.outputs,
                            params: stageItem.action.params
                        },
                        communicationChannel: stageItem.communicationChannel,
                        scheduling: stageItem.scheduling
                    })),
                    createdAt: configData.createdAt,
                    updatedAt: configData.updatedAt
                }
            };
        } catch (err: any) {
            return {
                success: false,
                error: "Error creating job configuration",
                details: err?.message || String(err)
            };
        }
    })
    .get("/:jobId", async ({ params: { jobId } }: { params: { jobId: string } }) => {
        try {
            const jobConfig = await JobConfigModel.findOne({ jobId }).lean();
            if (!jobConfig) {
                return { error: "Job configuration not found" };
            }

            // Cast to any to access timestamp fields
            const configData = jobConfig as any;

            // Return a clean, properly formatted response
            return {
                success: true,
                data: {
                    _id: configData._id,
                    jobId: configData.jobId,
                    flow: configData.flow.map((flowItem: any) => ({
                        stage: flowItem.stage,
                        next: flowItem.next.map((nextItem: any) => ({
                            stage: nextItem.stage,
                            outcome: nextItem.outcome
                        }))
                    })),
                    stageConfig: configData.stageConfig.map((stageItem: any) => ({
                        stage: stageItem.stage,
                        action: {
                            agentId: stageItem.action.agentId,
                            outputs: stageItem.action.outputs,
                            params: stageItem.action.params
                        },
                        communicationChannel: stageItem.communicationChannel,
                        scheduling: stageItem.scheduling
                    })),
                    createdAt: configData.createdAt,
                    updatedAt: configData.updatedAt
                }
            };
        } catch (err: any) {
            return { error: "Error fetching job configuration", details: err?.message || String(err) };
        }
    })
    .put("/:jobId", async ({ params: { jobId }, body }: { params: { jobId: string }; body: any }) => {
        try {
            const updatedJobConfig = await JobConfigModel.findOneAndUpdate(
                { jobId },
                body,
                { new: true, runValidators: true }
            );

            if (!updatedJobConfig) {
                return {
                    success: false,
                    error: "Job configuration not found"
                };
            }

            const configData = updatedJobConfig.toObject() as any;

            return {
                success: true,
                message: "Job configuration updated successfully",
                data: {
                    _id: configData._id,
                    jobId: configData.jobId,
                    flow: configData.flow.map((flowItem: any) => ({
                        stage: flowItem.stage,
                        next: flowItem.next.map((nextItem: any) => ({
                            stage: nextItem.stage,
                            outcome: nextItem.outcome
                        }))
                    })),
                    stageConfig: configData.stageConfig.map((stageItem: any) => ({
                        stage: stageItem.stage,
                        action: {
                            agentId: stageItem.action.agentId,
                            outputs: stageItem.action.outputs,
                            params: stageItem.action.params
                        },
                        communicationChannel: stageItem.communicationChannel,
                        scheduling: stageItem.scheduling
                    })),
                    createdAt: configData.createdAt,
                    updatedAt: configData.updatedAt
                }
            };
        } catch (err: any) {
            return {
                success: false,
                error: "Error updating job configuration",
                details: err?.message || String(err)
            };
        }
    })
    .put("/:jobId/stage/:stage", async ({ params: { jobId, stage }, body }: { params: { jobId: string; stage: string }; body: { next?: INextStatusConfig[]; scheduling?: IStageScheduling } }) => {
        try {
            const jobConfig = await JobConfigModel.findOne({ jobId });
            if (!jobConfig) {
                return { error: "Job configuration not found" };
            }

            const stageConfig = jobConfig.stageConfig.find((s) => s.stage === stage);
            if (!stageConfig) {
                return { error: "Stage not found in job configuration" };
            }

            // Restrict updates to outputs/outcomes
            if (body.next) {
                const statusConfig = jobConfig.flow.find((f) => f.stage === stage);
                if (!statusConfig) {
                    return { error: "Status configuration not found for the stage" };
                }
                statusConfig.next = body.next; // Update the next field
            }

            if (body.scheduling) {
                stageConfig.scheduling = body.scheduling; // Update scheduling if provided
            }

            await jobConfig.save();
            return jobConfig;
        } catch (err: any) {
            return { error: "Error updating stage action", details: err?.message || String(err) };
        }
    })
    .delete("/:jobId", async ({ params: { jobId } }) => {
        try {
            const deletedJobConfig = await JobConfigModel.findOneAndDelete({ jobId });
            if (!deletedJobConfig) {
                return { error: "Job configuration not found or already deleted" };
            }
            return { deletedJobConfig };
        } catch (err: any) {
            return { error: "Error deleting job configuration", details: err?.message || String(err) };
        }
    })
    .get("/analytics", async () => {
        try {
            const totalJobConfigs = await JobConfigModel.countDocuments();
            const stagesPerJob = await JobConfigModel.aggregate([
                { $unwind: "$stageConfig" },
                { $group: { _id: "$jobId", stageCount: { $sum: 1 } } },
            ]);
            return { totalJobConfigs, stagesPerJob };
        } catch (err: any) {
            return { error: "Error fetching job configuration analytics", details: err?.message || String(err) };
        }
    });
