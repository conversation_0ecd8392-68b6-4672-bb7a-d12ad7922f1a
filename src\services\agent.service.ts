import { assessmentAgent } from "../agents/assessment.agent";
import { reviewAgent } from "../agents/review.agent";
import { screeningAgent } from "../agents/screening.agent";

/**
 * Acts as a router to call the correct agent logic based on the agentId.
 * @param agentId The ID of the agent to execute (e.g., "reviewAgent").
 * @param params The parameters for that agent's task.
 * @param candidateId The ID of the candidate being processed.
 * @param jobId The ID of the job being processed.
 * @returns A promise that resolves with the agent's outcome.
 */
export const dispatchAgentTask = (
	agentId: string,
	params: any,
	outputs: string[],
	jobId: string,
	candidateId: string,
): Promise<void> => {
	switch (agentId) {
		case "reviewAgent":
			return reviewAgent(params, outputs, jobId, candidateId);

		case "screeningAgent":
			return screeningAgent(params, jobId, candidateId);

		case "assessmentAgent":
			return assessmentAgent(params, candidateId, jobId);

		default:
			console.error(`Unknown agentId: ${agentId}`);
			throw new Error(`No agent implementation found for agentId: ${agentId}`);
	}
};