import { Elysia, t } from "elysia";
import { env } from "../config/env";
import { AgentTask, TaskStatus } from "../models/agentTask.model";
import Candidate from "../models/candidate.model";
import Job from "../models/job.model";
import { Organization } from "../models/organization.model"; // Add this line
import { Recruiter } from "../models/recruiter.model";
import { sendEmail } from "../services/email.service";
import { webhookLogic } from "../services/handleWebhookLogic";
import { plivoScreeningCallService } from "../services/plivoScreeningCall";
import { sendOptInQuestionEmail } from "../utils/emailTemplates/sendOptInQuestionEmail";

export const vedaScreeningRoute = new Elysia({
	prefix: "/veda-screening",
})
	.post(
		"/update-status",
		async ({ body }) => {
			const { candidateId, phoneNumber, status, screeningCleared } = body;

			console.log(phoneNumber, status, screeningCleared);

			if (status === "CALL_STARTED") {
				await AgentTask.findOneAndUpdate(
					{
						candidateId,
						stage: "screening",
						status: TaskStatus.IN_PROGRESS, // Find the task that was started but not finished
					},
					{
						result: { plivoCallStatus: status },
					},
					{ sort: { createdAt: -1 } }, // Get the most recent one
				);
				return {
					status: true,
					message: "Call started successfully",
				};
			}

			if (screeningCleared !== undefined && status === "CALL_COMPLETED") {
				const isScreeningCleared =
					screeningCleared === "true" || screeningCleared === "1";
				const outcome = isScreeningCleared ? "pass" : "fail";
				await webhookLogic(candidateId, "screening", outcome, {
					plivoCallStatus: status,
				});
				return {
					status: true,
					message: "Screening completed successfully",
				};
			}

			const candidate = await Candidate.findById(candidateId);
			if (!candidate) {
				throw new Error(`Candidate not found with ID ${candidateId}`);
			}
			const job = await Job.findById(candidate.jobId);
			if (!job) {
				throw new Error(`Job not found with ID ${candidate.jobId}`);
			}

			const recruiter = await Recruiter.findById(job.createdBy);
			if (!recruiter) {
				console.error(
					`Recruiter or organization not found for job: ${candidate.jobId}`,
				);
				throw new Error("Recruiter or organization not found");
			}

			const organisation = await Organization.findById(recruiter.organization);
			if (!organisation) {
				console.error(`Organization not found for recruiter: ${recruiter._id}`);
				throw new Error("Organization not found");
			}

			const candidateEmail = candidate.email;
			const optInLink = `${env.UI_URL}/screening/${candidateId}`;

			const { subject, textContent } = sendOptInQuestionEmail({
				candidateName: candidate.name,
				jobTitle: job.title,
				companyName: organisation.name,
				optInLink,
			});

			const response = await sendEmail({
				toEmail: candidateEmail,
				subject,
				textContent,
			});

			if (!response.success) {
				throw new Error(
					`Optin email to the candidate: ${candidateEmail} failed`,
				);
			}

			const agentTask = await AgentTask.findOneAndUpdate(
				{
					candidateId,
					stage: "screening",
					status: TaskStatus.IN_PROGRESS, // Find the task that was started but not finished
				},
				{
					result: { plivoCallStatus: status, emailSent: true },
				},
				{ sort: { createdAt: -1 } }, // Get the most recent one
			);

			if (!agentTask) {
				throw new Error(
					`Webhook Error: Could not find an active AgentTask for candidate ${candidateId} at stage screening stage}`,
				);
			}

			return {
				status: true,
				message: "Status updated successfully",
			};
		},
		{
			body: t.Object({
				candidateId: t.String(),
				phoneNumber: t.Optional(t.String()),
				status: t.String(),
				screeningCleared: t.Optional(t.String()),
			}),
		},
	)
	.post(
		"/trigger-screening-call",
		async ({ body }) => {
			const { questions, companyName, role, to } = body;

			const webhook_endpoint = env.API_URL.replace("https://", "").replace(
				"http://",
				"",
			);

			const formattedTo = plivoScreeningCallService.formatPhoneNumber(to);

			const payload = {
				questions,
				companyName,
				role,
				to: formattedTo,
				webhook_endpoint,
			};

			console.log(payload);

			const response =
				await plivoScreeningCallService.initiateScreeningCall(payload);

			return {
				status: response.success,
				message: response.message,
			};
		},
		{
			body: t.Object({
				questions: t.String(),
				companyName: t.String(),
				role: t.String(),
				to: t.String(),
			}),
		},
	);
