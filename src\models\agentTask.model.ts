// agent-task.model.ts (updated)
import mongoose, { type Document, Schema, type Types } from "mongoose";

export enum TaskStatus {
	PENDING = "pending",
	IN_PROGRESS = "in_progress",
	COMPLETED = "completed",
	CANCELLED = "cancelled",
	FAILED = "failed",
}

export interface IAgentTask extends Document {
	jobId: Types.ObjectId;
	candidateId: Types.ObjectId;
	agentId: string;
	stage: string; // aligns with flow[*].stage
	status: TaskStatus;
	outcome?: string; // must match action.outputs when COMPLETED
	result?: unknown; // agent-returned payload
	error?: string; // error message if FAILED
	attempts: number;
	queueName?: string;
	startedAt?: Date;
	finishedAt?: Date;
	createdAt: Date;
	updatedAt: Date;
}

const agentTaskSchema = new Schema<IAgentTask>(
	{
		jobId: { type: Schema.Types.ObjectId, ref: "Job", required: true },
		candidateId: {
			type: Schema.Types.ObjectId,
			ref: "Candidate",
			required: true,
		},
		agentId: { type: String, required: true },
		stage: { type: String, required: true },
		status: {
			type: String,
			enum: Object.values(TaskStatus),
			default: TaskStatus.PENDING,
		},
		outcome: { type: String },
		result: { type: Schema.Types.Mixed },
		error: { type: String },
		attempts: { type: Number, default: 0 },
		queueName: { type: String },
		startedAt: { type: Date },
		finishedAt: { type: Date },
	},
	{ timestamps: true },
);

// Helpful indexes for routing & dashboards
agentTaskSchema.index({ jobId: 1, candidateId: 1, stage: 1, status: 1 });
agentTaskSchema.index({ stage: 1, status: 1, updatedAt: 1 });

export const AgentTask = mongoose.model<IAgentTask>(
	"AgentTask",
	agentTaskSchema,
);
