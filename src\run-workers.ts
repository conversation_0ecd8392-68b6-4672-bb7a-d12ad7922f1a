// src/run-workers.ts (Simplified)
import { Worker } from "bullmq";
import { closeDatabaseConnection, connectToDatabase } from "./config/mongodb";
import { redisConnection } from "./config/redisConnection";
import { JobConfigModel } from "./models/jobConfig.model";
import { processStageJob } from "./workers/workflow.processor";

const BATCH_SIZE = 10;

async function startWorkers() {
	console.log("🚀 Starting workers...");
	await connectToDatabase();
	console.log("✅ Worker process connected to MongoDB.");

	const allConfigs = await JobConfigModel.find({}).select("flow.stage").lean();
	const allStages = allConfigs.flatMap((config) =>
		config.flow.map((f) => f.stage),
	);
	const uniqueStages = [...new Set(allStages)].filter(
		(stage) => stage !== "stop" && stage !== "rejected",
	);

	if (uniqueStages.length === 0) {
		console.warn("No stages found in database. No workers will be started.");
		await closeDatabaseConnection();
		return;
	}

	console.log(
		`Found stages: [${uniqueStages.join(", ")}]. Initializing a worker for each.`,
	);

	for (const stageName of uniqueStages) {
		const worker = new Worker(stageName, processStageJob, {
			connection: redisConnection,
			concurrency: BATCH_SIZE,
		});

		// The 'completed' listener is now GONE.

		worker.on("failed", (job, error) => {
			console.error(
				`Job ${job?.id} failed in stage ${stageName}: ${error.message}`,
			);
		});
		worker.on("error", (error) => {
			console.error(`Worker error in stage ${stageName}:`, error);
		});
	}

	console.log("✅ All workers are up and running and listening for jobs.");
}

startWorkers().catch((err) => {
	console.error("Failed to start workers:", err);
	process.exit(1);
});

process.on("SIGINT", async () => {
	console.log("\nShutting down workers gracefully...");
	await closeDatabaseConnection();
	await redisConnection.quit();
	console.log("Connections closed. Exiting.");
	process.exit(0);
});
