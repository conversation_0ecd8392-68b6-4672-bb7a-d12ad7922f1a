import { SendEmailCommand } from "@aws-sdk/client-ses";
import { commonEmailParams, sesClient } from "../config/aws";

interface BaseEmailParams {
	toEmail: string;
	subject: string;
	textContent: string;
}

export const sendEmail = async ({
	toEmail,
	subject,
	textContent,
}: BaseEmailParams) => {
	const params = {
		...commonEmailParams,
		Destination: {
			ToAddresses: [toEmail],
		},
		Message: {
			Subject: {
				Data: subject,
				Charset: commonEmailParams.Charset,
			},
			Body: {
				Text: {
					Data: textContent,
					Charset: commonEmailParams.Charset,
				},
			},
		},
	};

	try {
		const command = new SendEmailCommand(params);
		await sesClient.send(command);
		return { success: true };
	} catch (error) {
		console.error("Error sending email:", error);
		throw error;
	}
};
