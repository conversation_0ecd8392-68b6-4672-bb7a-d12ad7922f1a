export interface ScreeningQuestion {
	id: string;
	question: string;
	options?: string[];
	correctAnswer?: string; // can be an option label (e.g., "Market range") or a letter (e.g., "B")
}

export function formatScreeningQuestions(
	questions: ScreeningQuestion[],
): string {
	const letter = (i: number) => String.fromCharCode(65 + i); // 0 -> A, 1 -> B, ...

	const normalizeCorrectAnswer = (q: ScreeningQuestion): string | null => {
		if (!q.correctAnswer) return null;
		const ans = q.correctAnswer.trim();
		const opts = q.options ?? [];

		// If answer is a single letter (A-Z), map to that option text when possible
		if (/^[A-Za-z]$/.test(ans)) {
			const idx = ans.toUpperCase().charCodeAt(0) - 65;
			if (idx >= 0 && idx < opts.length) return opts[idx] ?? null;
		}

		// If answer matches one of the options (case-insensitive), return the original option text
		const match = opts.find(
			(o) => o.trim().toLowerCase() === ans.toLowerCase(),
		);
		return match ?? ans; // fall back to the raw answer if not found in options
	};

	const parts = questions.map((q, i) => {
		const qNum = `Q${i + 1}: ${q.question.trim()}`;
		const options =
			q.options && q.options.length
				? ` Options: ${q.options.map((o, idx) => `${letter(idx)}. ${o.trim()}`).join(" ")}`
				: "";

		const correct = normalizeCorrectAnswer(q);
		const correctStr = correct ? `, Correct Answer: ${correct}` : "";

		// end each question with a period
		return `${qNum}${options}${correctStr}.`;
	});

	// space between questions, matching your example
	return parts.join(" ");
}
