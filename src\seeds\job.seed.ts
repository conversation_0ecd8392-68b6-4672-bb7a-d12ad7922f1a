import mongoose from "mongoose";
import { connectToDatabase } from "../config/mongodb";
import Job, {
	ExperienceLevel,
	type IJob,
	JobStatus,
	JobType,
	WorkLocation,
} from "../models/job.model";
import { Recruiter } from "../models/recruiter.model";

await connectToDatabase();

async function seedJobs() {
	// Fetch the first person from the database
	const firstPerson = await Recruiter.findOne({});
	if (!firstPerson) {
		console.error("No person found in the database.");
		process.exit(1);
	}

	const jobs: IJob[] = [
		{
			title: "Software Engineer",
			description: "Develop and maintain web applications.",
			department: "Engineering",
			location: "San Francisco, CA",
			jobType: JobType.FULL_TIME,
			workLocation: WorkLocation.ONSITE,
			experienceLevel: ExperienceLevel.MID,
			requiredSkills: [
				{ name: "JavaScript", level: "required", yearsRequired: 2 },
				{ name: "React", level: "preferred", yearsRequired: 1 },
				{ name: "Node.js", level: "nice_to_have" },
			],
			qualifications: [
				"Bachelor's degree in Computer Science",
				"2+ years experience",
			],
			responsibilities: [
				"Write clean code",
				"Collaborate with team",
				"Participate in code reviews",
			],
			salaryRange: {
				min: 90000,
				max: 120000,
				currency: "USD",
				period: "yearly",
			},
			status: JobStatus.ACTIVE,
			openings: 3,
			applicationDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
			startDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
			postedDate: new Date(),
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: firstPerson._id,
			organization: firstPerson.organization,
		},
		// Add more jobs as needed
	];

	const createdJobs = await Job.insertMany(jobs);

	console.log("Seeded jobs:", createdJobs);

	await mongoose.disconnect();
}

seedJobs().catch((err) => {
	console.error(err);
	process.exit(1);
});