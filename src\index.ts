import { swagger } from "@elysiajs/swagger";
import { Elysia } from "elysia";
import mongoose from "mongoose";
import { env } from "./config/env";
import { closeDatabaseConnection, connectToDatabase } from "./config/mongodb";
import { redisConnection } from "./config/redisConnection";
import { authApp } from "./routes/auth";
import { candidateRoute } from "./routes/candidate.route";
import { jobRoutes } from "./routes/job.route";
import { jobConfig } from "./routes/jobConfig.route";
import { jobsApp } from "./routes/jobs";
import { organizationRoute } from "./routes/organization.route";
import { recruiterRoute } from "./routes/recruiter.route";
import { vedaReviewRoute } from "./routes/vedaReview.route";
import { vedaScreeningRoute } from "./routes/vedaScreening.route";

await connectToDatabase();
new Elysia()
	.onRequest(({ request, set }) => {
		// Handle preflight requests first
		if (request.method === 'OPTIONS') {
			const origin = request.headers.get('origin');
			const allowedOrigins = ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:5173'];

			if (allowedOrigins.includes(origin || '')) {
				set.headers['Access-Control-Allow-Origin'] = origin || '';
			}
			set.headers['Access-Control-Allow-Credentials'] = 'true';
			set.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, PATCH, OPTIONS';
			set.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Cookie';
			set.status = 200;
			return '';
		}
	})
	.onAfterHandle(({ request, set }) => {
		// Add CORS headers to all responses
		const origin = request.headers.get('origin');
		const allowedOrigins = ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:5173'];

		if (allowedOrigins.includes(origin || '')) {
			set.headers['Access-Control-Allow-Origin'] = origin || '';
		}
		set.headers['Access-Control-Allow-Credentials'] = 'true';
		set.headers['Access-Control-Expose-Headers'] = 'Set-Cookie';
	})
	.onError(({ code, error, set }) => {
		console.error("Elysia error:", error);
		set.status = 500;
		return {
			status: "error",
			message: "message" in error ? error?.message : "Internal Server Error",
			code,
		};
	})
	.use(swagger())
	.get("/", async () => {
		return "Hello, Elysia! MongoDB is connected.";
	})
	.get("/health", async () => {
		const connectionState = mongoose.connection.readyState;
		const stateMap = {
			0: "disconnected",
			1: "connected",
			2: "connecting",
			3: "disconnecting",
		};

		return {
			status: "OK",
			version: "1.0.0",
			uptime: process.uptime(),
			mongodb: {
				status:
					connectionState === 1
						? "connected"
						: stateMap[connectionState as keyof typeof stateMap],
				dbName: mongoose.connection.db?.databaseName || env.DATABASE_NAME,
			},
		};
	})
	.use(authApp)
	.use(candidateRoute)
	.use(jobRoutes)
	.use(jobConfig)
	.use(jobsApp)
	.use(organizationRoute)
	.use(recruiterRoute)
	.use(vedaReviewRoute)
	.use(vedaScreeningRoute)
	.listen(env.PORT, async () => {
		console.log(`Elysia server is running at http://localhost:${env.PORT}`);
	});

process.on("SIGINT", async () => {
	await closeDatabaseConnection();
	await redisConnection.quit();
	process.exit(0);
});
