import Elysia  from "elysia";
import { ensureAuth } from "../services/auth";
import { Organization } from "../models/organization.model";
import type { IOrganizationBody, IAuthUser } from "../types/organization.types";

export const organizationRoute = new Elysia({prefix: "organization"})
    .use(ensureAuth)
    .get("/", async () => {
        try {
            const organizations = await Organization.find({});
            return organizations;
        } catch (error: any) {
            return { error: "Error fetching organizations", details: error?.message || String(error) };
        }
    })
    .get("/:id", async ({ params: { id } }) => {
        if (!id) {
            return { error: "No id is provided" };
        }
        try {
            const organization = await Organization.findById(id);
            if (!organization) {
                return { error: "Organization not found" };
            }
            return organization;
        } catch (error: any) {
            return { error: "Error fetching organization", details: error?.message || String(error) };
        }
    })
    .patch("/update/:id", async({ params: { id }, body, user }: { params: { id: string }, body: IOrganizationBody, user: IAuthUser }) => {
        if (!id) {
            return { error: "No organization id provided" };
        }
        if (!user || user.organization?.toString() !== id) {
            return { error: "Unauthorized: You can only update your own organization" };
        }
        // Only allow updating allowed fields
        const { name, address, domain } = body;
        const updateFields: Record<string, any> = {};
        if (name !== undefined) updateFields.name = name;
        if (address !== undefined) updateFields.address = address;
        if (domain !== undefined) updateFields.domain = domain;
        if (Object.keys(updateFields).length === 0) {
            return { error: "No valid fields provided for update" };
        }
        try {
            const updatedOrg = await Organization.findByIdAndUpdate(
                id,
                { $set: updateFields },
                { new: true, runValidators: true }
            );
            if (!updatedOrg) {
                return { error: "Organization not found or update failed" };
            }
            return updatedOrg;
        } catch (err: any) {
            return { error: "Error updating organization", details: err?.message || String(err) };
        }
    })
    .delete("/delete/:id", async ({ params: { id }, user }: { params: { id: string }, user: IAuthUser }) => {
        if (!id) {
            return { error: "Organization id not provided" };
        }
        if (!user || user.organization !== id) {
            return { error: "Unauthorized: You can only delete your own organization" };
        }
        try {
            const deletedOrganization = await Organization.findByIdAndDelete(id);
            if (!deletedOrganization) {
                return { error: "Organization not found or already deleted" };
            }
            return { deletedOrganization };
        } catch (error: any) {
            return { error: "Error deleting organization", details: error?.message || String(error) };
        }
    })
    .post("/", async ({ body }: { body: IOrganizationBody }) => {
        const { name, address, domain } = body;
        if (!name || !domain) {
            return { error: "Name and domain are required" };
        }
        try {
            const newOrganization = await Organization.create({ name, address, domain });
            return newOrganization;
        } catch (error: any) {
            return { error: "Error creating organization", details: error?.message || String(error) };
        }
    })
    .get("/analytics", async () => {
        try {
            const totalOrganizations = await Organization.countDocuments();
            return { totalOrganizations };
        } catch (error: any) {
            return { error: "Error fetching analytics", details: error?.message || String(error) };
        }
    })
    .get("/search", async ({ query }: { query: { name?: string; domain?: string; address?: string } }) => {
        const searchCriteria: Record<string, any> = {};
        if (query.name) searchCriteria.name = { $regex: query.name, $options: "i" };
        if (query.domain) searchCriteria.domain = { $regex: query.domain, $options: "i" };
        if (query.address) searchCriteria.address = { $regex: query.address, $options: "i" };
        try {
            const organizations = await Organization.find(searchCriteria);
            return organizations;
        } catch (error: any) {
            return { error: "Error searching organizations", details: error?.message || String(error) };
        }
    });
