import { openaiClient } from "../config/openai"; // Assuming your OpenAI client is configured and exported from this path.

// Define the structure for the assessment questions
export interface AssessmentQuestion {
	topic: string;
	questionText: string;
	followUpQuestions: string[];
}

// Define the structure for the transcript entries
interface TranscriptEntry {
	type: "user" | "system";
	content: string;
}

// Define the structure for the expected JSON response from OpenAI
interface AnalysisResult {
	outcome: "qualified" | "not-qualified";
	reasoning: string;
}

/**
 * Analyzes an interview transcript against job details and questions asked to determine if a candidate is qualified.
 * This function is designed to be production-ready for both technical and non-technical roles.
 *
 * @param jobDetails A string containing the detailed description of the job.
 * @param transcript An array of objects representing the conversation log of the interview.
 * @param questions An array of the specific questions asked during the assessment.
 * @returns A promise that resolves to an object containing the 'outcome' and 'reasoning'.
 */
export const analyzeInterview = async (
	jobDetails: string,
	transcript: TranscriptEntry[],
	questions: AssessmentQuestion[],
): Promise<AnalysisResult> => {
	// 1. Format the transcript and questions into readable strings for the AI model.
	const formattedTranscript = transcript
		.map(
			(entry) =>
				`${entry.type === "system" ? "Interviewer" : "Candidate"}: ${
					entry.content
				}`,
		)
		.join("\n");

	const formattedQuestions = questions
		.map(
			(q, i) =>
				`Question ${i + 1} (Topic: ${q.topic}):\n- ${q.questionText}${
					q.followUpQuestions.length > 0
						? `\n- Follow-ups: ${q.followUpQuestions.join(", ")}`
						: ""
				}`,
		)
		.join("\n\n");

	// 2. Construct a more sophisticated system prompt for a comprehensive, production-level analysis.
	const systemPrompt = `
        You are a senior hiring manager and an expert evaluator of talent. Your task is to perform a detailed analysis of a candidate's interview.
        You will be given:
        1.  **Job Details:** The requirements and responsibilities for the role.
        2.  **Interview Questions:** The specific questions that were asked to the candidate.
        3.  **Interview Transcript:** The full conversation between the interviewer and the candidate.

        Your analysis must be comprehensive, covering:
        - **Knowledge & Skills:** Did the candidate's answers demonstrate the required technical or domain knowledge mentioned in the job details? Evaluate the depth and accuracy of their responses to the questions asked.
        - **Problem-Solving:** How did the candidate approach the problems presented in the questions?
        - **Communication:** Was the candidate clear, concise, and professional?
        - **Overall Fit:** Based on the transcript and job details, is this candidate a strong match for the role and the company?

        You MUST respond with a JSON object containing exactly two keys: "outcome" and "reasoning".
        - "outcome": Must be either "qualified" or "not-qualified".
        - "reasoning": Must be a detailed, evidence-based explanation for your decision. Reference specific answers from the transcript and link them to the job requirements and the questions asked. For a "qualified" outcome, explain why they are a strong fit. For a "not-qualified" outcome, clearly state the gaps or red flags.

        Do not include any other text, markdown, or explanations outside of this JSON object. The interview could be for a technical or a non-technical role; adapt your evaluation criteria accordingly.
    `;

	// 3. Construct the user prompt with all the necessary context.
	const userPrompt = `
        **JOB DETAILS:**
        ${jobDetails}

        ---

        **INTERVIEW QUESTIONS ASKED:**
        ${formattedQuestions}

        ---

        **INTERVIEW TRANSCRIPT:**
        ${formattedTranscript}
    `;

	try {
		// 4. Call the OpenAI Chat Completions API with the enhanced prompts.
		const response = await openaiClient.chat.completions.create({
			model: "gpt-4o", // Recommended for complex reasoning tasks.
			response_format: { type: "json_object" },
			messages: [
				{ role: "system", content: systemPrompt },
				{ role: "user", content: userPrompt },
			],
			temperature: 0.2, // Keep temperature low for more factual and consistent evaluations.
		});

		const resultString = response.choices[0]?.message?.content;

		if (!resultString) {
			throw new Error("OpenAI API returned an empty response.");
		}

		// 5. Parse and validate the JSON response.
		const analysisResult: AnalysisResult = JSON.parse(resultString);

		if (
			!analysisResult.outcome ||
			!analysisResult.reasoning ||
			!["qualified", "not-qualified"].includes(analysisResult.outcome)
		) {
			throw new Error("Invalid JSON structure received from OpenAI.");
		}

		return analysisResult;
	} catch (error) {
		console.error("Error during interview analysis:", error);
		// Provide a robust fallback response.
		return {
			outcome: "not-qualified",
			reasoning:
				"An error occurred during the AI analysis. The evaluation could not be completed. Please review the transcript manually.",
		};
	}
};
