import mongoose, { Types } from "mongoose";
import { connectToDatabase } from "../config/mongodb";
import { Organization } from "../models/organization.model";
import { type IRecruiter, Recruiter } from "../models/recruiter.model";

await connectToDatabase();

async function seedRecruiters() {
	// Find organizations to associate recruiters
	const organizations = await Organization.find({});
	const organization = organizations[0];
	if (!organization) {
		throw new Error("No organizations found. Seed organizations first.");
	}

	const recruiters: IRecruiter[] = [
		{
			_id: new Types.ObjectId(),
			name: "<PERSON><PERSON><PERSON>",
			email: "<EMAIL>",
			organization: organization._id,
			password: "john@54321",
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			_id: new Types.ObjectId(),
			name: "Amit Saharan",
			email: "<EMAIL>",
			organization: organization._id,
			password: "jane@54321",
			createdAt: new Date(),
			updatedAt: new Date(),
		},
	];

	const createdRecruiters = await Recruiter.insertMany(recruiters);

	console.log("Seeded recruiters:", createdRecruiters);

	await mongoose.disconnect();
}

seedRecruiters().catch((err) => {
	console.error(err);
	process.exit(1);
});
