# Veda

A comprehensive TypeScript-based backend application for automated interview scheduling, candidate screening, and resume review. Built with modern technologies including Elysia, MongoDB, Redis, BullMQ, OpenAI, and Plivo for intelligent recruitment automation.

## 🚀 Tech Stack

- **Runtime**: [Bun](https://bun.sh) - Fast all-in-one JavaScript runtime
- **Web Framework**: [Elysia](https://elysiajs.com) with Swagger documentation
- **Database**: MongoDB with Mongoose ODM
- **Cache & Queue**: Redis with BullMQ for job processing
- **AI Integration**: OpenAI for intelligent resume analysis
- **Communication**: Plivo for automated phone screening
- **PDF Processing**: unpdf for resume parsing
- **Language**: TypeScript with strict configuration
- **Validation**: Envalid for environment variables, Z<PERSON> for data validation
- **Linting**: Biome

## ✨ Features

### Core Functionality
- **RESTful API** with comprehensive Swagger documentation
- **MongoDB integration** with well-defined data models
- **Redis-based job queue system** for background processing
- **AI-powered resume analysis** using OpenAI
- **Automated phone screening** via Plivo integration
- **PDF resume parsing** and text extraction

### Intelligent Agents
- **Review Agent**: AI-powered resume analysis and candidate scoring
- **Screening Agent**: Automated phone call screening with dynamic questions
- **Workflow Management**: Configurable multi-stage recruitment pipelines

### Advanced Features
- **Multi-tenant architecture** with organization support
- **Authentication system** with role-based access
- **External ATS integration** capabilities
- **Webhook support** for real-time updates
- **Graceful shutdown handling**
- **Comprehensive error handling and logging**

## 📋 Prerequisites

- [Bun](https://bun.sh) (v1.2.17 or later)
- MongoDB (running locally or accessible via URI)
- Redis server (for job queue functionality)
- OpenAI API key (for AI features)
- Plivo account credentials (for phone screening)

## 🛠 Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd veda
bun install
```

2. **Environment Setup:**
Create a `.env` file in the root directory:

```bash
cp env.example .env
```

Required environment variables:
```plaintext
# Application
NODE_ENV=development
PORT=8080
API_URL=http://localhost:8080

# Database
MONGO_URI=mongodb://localhost:27017
DATABASE_NAME=veda-backend

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# Plivo (for phone screening)
PLIVO_AUTH_ID=your_plivo_auth_id
PLIVO_AUTH_TOKEN=your_plivo_auth_token
PLIVO_PHONE_NUMBER=your_plivo_phone_number

# Additional service configurations
```

## 🚀 Development

### Start the main application:
```bash
bun run dev
```

### Start background workers:
```bash
bun run workers
```

### Seed test data:
```bash
bun run seed:candidates
```

The server will start at `http://localhost:8080` with Swagger documentation available at `/swagger`.

## 📊 Available Scripts

- `bun run dev` - Start development server with hot reload
- `bun run workers` - Start background job workers
- `bun run lint` - Run Biome linter
- `bun run seed:candidates` - Seed database with test candidate data

## 🌐 API Endpoints

### Health & Status
- **GET** `/health` - Application health status with database connectivity
- **GET** `/` - Basic status endpoint

### Authentication
- **POST** `/auth/login` - User authentication
- **POST** `/auth/register` - User registration
- **GET** `/auth/profile` - User profile information

### Job Management
- **GET** `/jobs` - List all jobs
- **POST** `/jobs` - Create new job
- **GET** `/jobs/:id` - Get job details
- **PUT** `/jobs/:id` - Update job
- **DELETE** `/jobs/:id` - Delete job

### Screening System
- **POST** `/plivo/screening/initiate` - Start automated phone screening
- **POST** `/plivo/screening/webhook` - Handle screening call results

### Documentation
- **GET** `/swagger` - Interactive API documentation

## 🏗 Project Structure

```
src/
├── agents/                    # AI-powered processing agents
│   ├── review.agent.ts       # Resume analysis and scoring
│   └── screening.agent.ts    # Phone screening automation
├── config/                   # Configuration files
│   ├── env.ts               # Environment validation
│   ├── mongodb.ts           # Database connection
│   ├── openai.ts            # OpenAI client setup
│   └── redisConnection.ts   # Redis configuration
├── models/                   # MongoDB data models
│   ├── agentTask.model.ts   # Background task tracking
│   ├── candidate.model.ts   # Candidate information
│   ├── job.model.ts         # Job postings
│   ├── jobConfig.model.ts   # Workflow configurations
│   ├── organization.model.ts # Company/organization data
│   └── recruiter.model.ts   # Recruiter profiles
├── routes/                   # API route handlers
│   ├── auth.ts              # Authentication endpoints
│   ├── job.route.ts         # Job management
│   ├── jobs.ts              # Job listing and CRUD
│   └── plivoScreening.route.ts # Phone screening
├── services/                 # Business logic services
│   ├── auth/                # Authentication services
│   ├── agent.service.ts     # Agent orchestration
│   ├── mockExternalATS.service.ts # ATS integration
│   ├── plivoScreeningCall.ts # Phone call management
│   ├── queue.service.ts     # Job queue management
│   └── workFlow.service.ts  # Workflow execution
├── types/                    # TypeScript type definitions
├── utils/                    # Utility functions
├── workers/                  # Background job processors
├── index.ts                  # Main application entry
└── run-workers.ts           # Worker process entry
```

## 🤖 AI-Powered Features

### Resume Analysis
- **Automated parsing** of PDF resumes using unpdf
- **AI-powered evaluation** against job requirements
- **Scoring system** based on configurable criteria
- **Skills extraction** and matching
- **Experience validation**

### Phone Screening
- **Dynamic question generation** based on job requirements
- **Automated call initiation** via Plivo
- **Real-time response processing**
- **Scoring and qualification assessment**
- **Webhook-based result handling**

## 🔄 Workflow System

### Configurable Pipelines
- **Multi-stage recruitment workflows**
- **Conditional logic and branching**
- **Agent task orchestration**
- **Status tracking and reporting**
- **Custom evaluation criteria**

### Job Configuration
Each job can define:
- **Review criteria and minimum scores**
- **Screening questions and format**
- **Communication channels**
- **Workflow progression rules**
- **Success/failure conditions**

## 🗄 Data Models

### Core Entities
- **Organization**: Company/tenant information
- **Recruiter**: User accounts with org association
- **Job**: Job postings with requirements and config
- **Candidate**: Applicant profiles and contact info
- **AgentTask**: Background processing tasks

### Workflow Configuration
- **JobConfig**: Defines multi-stage recruitment pipelines
- **Agent Parameters**: Configurable AI behavior
- **Status Transitions**: Workflow state management

## 🐳 Docker Support

For local development with Docker:

```yaml
# docker-compose.yml
version: '3.8'
services:
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongodb_data:
  redis_data:
```

## 🔧 Configuration

### Environment Variables
The application uses Envalid for runtime validation of environment variables. All required variables must be set before starting the application.

### AI Configuration
- **OpenAI Integration**: Configure models and parameters for resume analysis
- **Plivo Setup**: Phone number configuration and webhook endpoints
- **Resume Processing**: PDF parsing and text extraction settings

## 🚦 Queue System

### Background Processing
- **Screening Queue**: Handles phone call automation
- **Review Queue**: Processes resume analysis tasks
- **Notification Queue**: Manages communication workflows

### Features
- **Automatic retries** with exponential backoff
- **Dead letter queues** for failed jobs
- **Job progress tracking**
- **Concurrent processing** with rate limiting

## 🔐 Security Features

- **Environment variable validation**
- **Input sanitization** with Zod schemas
- **Authentication middleware**
- **Rate limiting** and request validation
- **Secure webhook handling**

## 🧪 Testing & Development

### Data Seeding
Use the provided seeding scripts to populate your development database:
```bash
bun run seed:candidates
```

### Linting
Maintain code quality with Biome:
```bash
bun run lint
```

## 🤝 Contributing

1. **Environment Setup**: Ensure all required services (MongoDB, Redis) are running
2. **Code Quality**: Run `bun run lint` before committing
3. **TypeScript**: Follow strict mode guidelines
4. **Testing**: Add appropriate error handling and logging
5. **Documentation**: Update API documentation for new endpoints

## 📄 License

Private project - All rights reserved.

---

**Veda** - Intelligent Recruitment Automation Platform