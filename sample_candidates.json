{"csvData": [{"name": "<PERSON>", "email": "<EMAIL>", "phone": "+1444987654", "resumeLink": "https://example.com/resume-sarah.pdf", "jobId": "688355938e98c28d583d022a", "stage": "registered", "source": "referral", "expectedSalary": 81000, "contactInfo.email": "<EMAIL>", "contactInfo.phone": "+1444987654", "contactInfo.linkedin": "https://linkedin.com/in/sarah<PERSON>lson", "contactInfo.github": "", "contactInfo.address": "741 Aspen Way"}, {"name": "<PERSON>", "email": "micha<PERSON>.<EMAIL>", "phone": "+1555123456", "resumeLink": "https://example.com/resume-michael.pdf", "jobId": "688355938e98c28d583d022a", "stage": "registered", "source": "job_board", "expectedSalary": 95000, "contactInfo.email": "micha<PERSON>.<EMAIL>", "contactInfo.phone": "+1555123456", "contactInfo.linkedin": "https://linkedin.com/in/micha<PERSON><PERSON>", "contactInfo.github": "https://github.com/mchen", "contactInfo.address": "123 Tech Street"}, {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+1666789012", "resumeLink": "https://example.com/resume-jennifer.pdf", "jobId": "68893375fdd070fe6d446137", "stage": "registered", "source": "linkedin", "expectedSalary": 78000, "contactInfo.email": "<EMAIL>", "contactInfo.phone": "+1666789012", "contactInfo.linkedin": "https://linkedin.com/in/jenniferwhite", "contactInfo.github": "", "contactInfo.address": "456 Innovation Ave"}]}