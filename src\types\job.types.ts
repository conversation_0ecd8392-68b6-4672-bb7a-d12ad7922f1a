export interface IJob {
    title: string;
    description: string;
    department: string;
    location: string;
    jobType: string;
    workLocation: string;
    experienceLevel: string;
    requiredSkills: Array<{
        name: string;
        level: "nice_to_have" | "required" | "preferred";
        yearsRequired?: number;
    }>;
    qualifications: string[];
    responsibilities: string[];
    salaryRange?: {
        min: number;
        max: number;
        currency: string;
        period: "hourly" | "monthly" | "yearly";
    };
    status: string;
    openings: number;
    applicationDeadline?: Date;
    startDate?: Date;
    createdBy: string;
    organization: string;
}
