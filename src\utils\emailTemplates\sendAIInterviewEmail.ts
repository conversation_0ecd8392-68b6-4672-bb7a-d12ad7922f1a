interface AIInterviewEmailParams {
	candidateName: string;
	jobTitle: string;
	companyName: string;
	aiInterviewLink: string;
}

export const sendAIInterviewEmail = ({
	candidateName,
	jobTitle,
	companyName,
	aiInterviewLink,
}: AIInterviewEmailParams) => {
	const subject = `Your AI Interview for ${jobTitle} at ${companyName}`;

	const greeting = `Hi ${candidateName},`;

	const textContent = `${greeting}

        You're invited to complete an AI-based interview for the position of ${jobTitle} at ${companyName}.

        ✅ This is a one-way interview – you can take it at your convenience.
        ✅ Your responses will help the hiring team better understand your fit for the role.
        ✅ You can complete this on any device with a camera and microphone.

        To begin the interview, please click the link below:
        ${aiInterviewLink}

        If you have any questions or face issues, feel free to reach out to us.

        Good luck!
        Team ${companyName}
`;

	return {
		subject,
		textContent,
	};
};
