import Elysia, { t } from "elysia";
import { Recruiter } from "../models/recruiter.model";
import { ensureAuth } from "../services/auth";

export const authApp = new Elysia({ prefix: "/auth" })
	.post(
		"/login",
		async ({ body, cookie }) => {
			const recruiter = await Recruiter.findOne({ email: body.email });
			if (!recruiter) return { error: "User not found" };
			if (!(await recruiter.comparePassword(body.password)))
				return { error: "Invalid password" };
			const token = recruiter.generateToken();
			cookie.authToken?.set({
				value: token,
			});
			return { token };
		},
		{
			body: t.Object({
				email: t.String(),
				password: t.String(),
			}),
		},
	)
	.use(ensureAuth)
	.get("/me", async ({ user }) => {
		try {
			const recruiter = await Recruiter.findById(user.id).populate('organization');
			if (!recruiter) {
				return { error: "User not found" };
			}
			// Return user info without password
			const { password, ...userInfo } = recruiter.toObject();
			return userInfo;
		} catch (error) {
			return { error: "Failed to fetch user information" };
		}
	});
