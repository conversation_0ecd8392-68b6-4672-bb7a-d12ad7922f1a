import { toZonedTime } from "date-fns-tz";
import Candidate, { StageStatus } from "../models/candidate.model";
import {
	type IStageScheduling,
	JobConfigModel,
} from "../models/jobConfig.model";
import { getQueue } from "./queue.service";

/**
 * Calculates the delay needed before a job should be executed based on scheduling rules.
 * @param scheduling - The scheduling configuration for the stage.
 * @returns The delay in milliseconds.
 */
function calculateDelay(scheduling: IStageScheduling | undefined): number {
	// Default to immediate execution if no scheduling is defined.
	if (!scheduling || scheduling.type === "IMMEDIATE") {
		return 0;
	}

	if (scheduling.type === "BUSINESS_HOURS") {
		const {
			timezone = "Asia/Kolkata",
			startHour = 10,
			endHour = 20,
		} = scheduling.params || {};
		const now = new Date();
		const nowInZone = toZonedTime(now, timezone);
		const currentHour = nowInZone.getHours();

		const executionTime = new Date(nowInZone);
		if (currentHour >= endHour) {
			// After business hours today
			executionTime.setDate(executionTime.getDate() + 1);
			executionTime.setHours(startHour, 0, 0, 0);
		} else if (currentHour < startHour) {
			// Before business hours today
			executionTime.setHours(startHour, 0, 0, 0);
		}
		// If we are within business hours, executionTime will be in the past or now, so delay will be <= 0

		const delay = executionTime.getTime() - nowInZone.getTime();
		return Math.max(0, delay); // Ensure delay is not negative
	}

	return 0;
}

/**
 * The main orchestrator. Determines the next stage for a candidate and queues them up
 * according to the defined workflow and scheduling rules.
 * @param candidateId - The ID of the candidate to process.
 * @param previousStageOutcome - The result from the previous stage (e.g., "best", "pass", or "START" for initial trigger).
 */
export const triggerNextStage = async (
	candidateId: string,
	previousStageOutcome: string,
) => {
	// 1. Fetch the candidate and their job configuration
	const candidate = await Candidate.findById(candidateId);
	if (!candidate) {
		console.error(`Orchestrator Error: Candidate ${candidateId} not found.`);
		return;
	}

	const jobConfig = await JobConfigModel.findOne({ jobId: candidate.jobId });
	if (!jobConfig) {
		console.error(
			`Orchestrator Error: JobConfig for job ${candidate.jobId} not found.`,
		);
		return;
	}

	// 2. Determine the next stage based on the outcome
	let nextStageName: string;
	if (previousStageOutcome === "START") {
		nextStageName = jobConfig.flow[0]?.stage || "";
	} else {
		const currentFlow = jobConfig.flow.find((f) => f.stage === candidate.stage);
		nextStageName =
			currentFlow?.next.find((n) => n.outcome === previousStageOutcome)
				?.stage || "";
	}

	if (!nextStageName) {
		console.log(
			`Workflow for candidate ${candidateId} ends. No next stage found for outcome '${previousStageOutcome}'.`,
		);
		return;
	}

	// 3. Handle terminal stages
	if (nextStageName === "stop" || nextStageName === "rejected") {
		console.log(
			`🏁 Workflow for candidate ${candidateId} has terminated at stage: '${nextStageName}'.`,
		);
		candidate.status = StageStatus.WORKFLOW_TERMINATED;
		candidate.stage = nextStageName as any; // Update to the terminal stage name
		await candidate.save();
		return;
	}

	// 4. Find the configuration and scheduling rules for the next stage
	const nextStageConfig = jobConfig.stageConfig.find(
		(sc) => sc.stage === nextStageName,
	);
	if (!nextStageConfig) {
		console.error(
			`Orchestrator Error: Config for stage '${nextStageName}' not found.`,
		);
		return;
	}

	// 5. Calculate delay and enqueue the job
	const delay = calculateDelay(nextStageConfig.scheduling);
	const queue = getQueue(nextStageName);
	const jobData = {
		candidateId: (candidate._id as string).toString(),
		jobId: candidate.jobId.toString(),
		currentStage: nextStageName,
	};

	await queue.add(`process-${nextStageName}-${candidateId}`, jobData, {
		delay,
	});

	// 6. Update the candidate's state to be the source of truth
	candidate.stage = nextStageName as any;
	candidate.status = StageStatus.QUEUED;
	await candidate.save();

	console.log(
		`✅ Candidate ${candidateId} successfully queued for stage '${nextStageName}' with a delay of ${Math.round(delay / 1000)}s.`,
	);
};
